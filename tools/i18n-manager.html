<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>i18n 翻译管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h3 {
            color: #374151;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .file-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .file-card {
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 15px;
            background: #f9fafb;
        }
        
        .file-card h4 {
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .key-list {
            max-height: 200px;
            overflow-y: auto;
            font-size: 14px;
        }
        
        .key-item {
            padding: 4px 0;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
        }
        
        .key-name {
            font-weight: 500;
            color: #374151;
        }
        
        .key-value {
            color: #6b7280;
            font-style: italic;
        }
        
        .missing {
            color: #dc2626;
            font-weight: bold;
        }
        
        .tools {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 i18n 翻译管理工具</h1>
            <p>管理和检查项目的国际化翻译文件</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h3>📊 翻译统计</h3>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalKeys">-</div>
                        <div class="stat-label">总翻译键数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalLanguages">-</div>
                        <div class="stat-label">支持语言数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="missingKeys">-</div>
                        <div class="stat-label">缺失翻译</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="usedKeys">-</div>
                        <div class="stat-label">已使用键数</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h3>🛠️ 工具</h3>
                <div class="tools">
                    <button class="btn btn-primary" onclick="loadTranslations()">🔄 重新加载</button>
                    <button class="btn btn-secondary" onclick="exportFlat()">📤 导出扁平化文件</button>
                    <button class="btn btn-secondary" onclick="checkMissing()">🔍 检查缺失翻译</button>
                </div>
            </div>
            
            <div class="section">
                <h3>📁 翻译文件</h3>
                <div class="file-list" id="fileList">
                    <div class="file-card">
                        <h4>加载中...</h4>
                        <p>正在读取翻译文件...</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h3>🔑 使用的翻译键</h3>
                <div id="usedKeysList">
                    <p>点击"重新加载"按钮来扫描使用的翻译键</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 模拟数据 - 在实际使用中，这些数据应该从服务器或文件系统获取
        const mockData = {
            languages: {
                'zh-CN': {
                    common: {
                        okText: '确认',
                        cancelText: '取消',
                        saveText: '保存',
                        deleteText: '删除'
                    },
                    system: {
                        admin: {
                            smsSetting: '短信设置'
                        }
                    }
                },
                'en': {
                    common: {
                        okText: 'OK',
                        cancelText: 'Cancel',
                        saveText: 'Save'
                        // 注意：deleteText 缺失
                    },
                    system: {
                        admin: {
                            smsSetting: 'SMS Setting'
                        }
                    }
                }
            },
            usedKeys: [
                'common.okText',
                'common.cancelText',
                'common.saveText',
                'common.deleteText',
                'system.admin.smsSetting',
                'common.nonExistentKey' // 这个键不存在
            ]
        };
        
        function loadTranslations() {
            // 更新统计
            updateStats();
            
            // 显示文件列表
            displayFileList();
            
            // 显示使用的键
            displayUsedKeys();
        }
        
        function updateStats() {
            const languages = Object.keys(mockData.languages);
            const allKeys = new Set();
            
            // 收集所有键
            languages.forEach(lang => {
                const keys = getAllKeys(mockData.languages[lang]);
                keys.forEach(key => allKeys.add(key));
            });
            
            document.getElementById('totalKeys').textContent = allKeys.size;
            document.getElementById('totalLanguages').textContent = languages.length;
            document.getElementById('usedKeys').textContent = mockData.usedKeys.length;
            
            // 计算缺失翻译
            let missingCount = 0;
            allKeys.forEach(key => {
                languages.forEach(lang => {
                    if (!hasKey(mockData.languages[lang], key)) {
                        missingCount++;
                    }
                });
            });
            
            document.getElementById('missingKeys').textContent = missingCount;
        }
        
        function displayFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            Object.keys(mockData.languages).forEach(lang => {
                const card = document.createElement('div');
                card.className = 'file-card';
                
                const keys = getAllKeys(mockData.languages[lang]);
                
                card.innerHTML = `
                    <h4>📄 ${lang}</h4>
                    <div class="key-list">
                        ${keys.map(key => {
                            const value = getKeyValue(mockData.languages[lang], key);
                            return `
                                <div class="key-item">
                                    <span class="key-name">${key}</span>
                                    <span class="key-value">${value}</span>
                                </div>
                            `;
                        }).join('')}
                    </div>
                `;
                
                fileList.appendChild(card);
            });
        }
        
        function displayUsedKeys() {
            const container = document.getElementById('usedKeysList');
            container.innerHTML = `
                <div class="key-list">
                    ${mockData.usedKeys.map(key => {
                        const exists = Object.keys(mockData.languages).some(lang => 
                            hasKey(mockData.languages[lang], key)
                        );
                        
                        return `
                            <div class="key-item">
                                <span class="key-name ${exists ? '' : 'missing'}">${key}</span>
                                <span class="key-value">${exists ? '✅ 存在' : '❌ 缺失'}</span>
                            </div>
                        `;
                    }).join('')}
                </div>
            `;
        }
        
        function getAllKeys(obj, prefix = '') {
            const keys = [];
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    const fullKey = prefix ? `${prefix}.${key}` : key;
                    if (typeof obj[key] === 'object' && obj[key] !== null) {
                        keys.push(...getAllKeys(obj[key], fullKey));
                    } else {
                        keys.push(fullKey);
                    }
                }
            }
            return keys;
        }
        
        function hasKey(obj, key) {
            const keys = key.split('.');
            let current = obj;
            for (const k of keys) {
                if (current && typeof current === 'object' && k in current) {
                    current = current[k];
                } else {
                    return false;
                }
            }
            return true;
        }
        
        function getKeyValue(obj, key) {
            const keys = key.split('.');
            let current = obj;
            for (const k of keys) {
                if (current && typeof current === 'object' && k in current) {
                    current = current[k];
                } else {
                    return '未找到';
                }
            }
            return current;
        }
        
        function exportFlat() {
            alert('导出功能需要后端支持，请使用 scripts/generateFlatI18n.js 脚本');
        }
        
        function checkMissing() {
            alert('检查功能需要后端支持，请使用 scripts/checkI18n.js 脚本');
        }
        
        // 页面加载时自动加载数据
        window.onload = loadTranslations;
    </script>
</body>
</html>
