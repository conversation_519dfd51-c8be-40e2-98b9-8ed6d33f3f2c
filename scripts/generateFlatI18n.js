/**
 * 生成扁平化的 i18n 文件，用于 i18n Ally 插件识别
 * 这个脚本会读取现有的嵌套翻译文件，生成扁平化的 JSON 文件
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 递归扁平化对象
function flattenObject(obj, prefix = '') {
  const flattened = {}

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key

      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObject(obj[key], newKey))
      } else {
        flattened[newKey] = obj[key]
      }
    }
  }

  return flattened
}

// 解析 TypeScript 文件内容，转换为 JSON 对象
function parseTsFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')

    // 提取 export default 后的对象内容
    const match = content.match(/export\s+default\s+({[\s\S]*})/m)
    if (match) {
      let objectContent = match[1]

      // 清理注释
      objectContent = objectContent.replace(/\/\/.*$/gm, '')
      objectContent = objectContent.replace(/\/\*[\s\S]*?\*\//g, '')

      // 简单的键值对解析
      const result = {}

      // 匹配键值对：key: 'value' 或 key: "value"
      const keyValueRegex = /(\w+):\s*['"`]([^'"`]*?)['"`]/g
      let keyMatch

      while ((keyMatch = keyValueRegex.exec(objectContent)) !== null) {
        const key = keyMatch[1]
        const value = keyMatch[2]
        result[key] = value
      }

      // 如果没有找到任何键值对，尝试更宽松的匹配
      if (Object.keys(result).length === 0) {
        // 匹配更复杂的模式，包括多行字符串
        const complexRegex = /(\w+):\s*['"`]([\s\S]*?)['"`](?=,|\s*})/g
        let complexMatch

        while ((complexMatch = complexRegex.exec(objectContent)) !== null) {
          const key = complexMatch[1]
          const value = complexMatch[2].replace(/\s+/g, ' ').trim()
          result[key] = value
        }
      }

      return result
    }

    console.warn(`文件 ${filePath} 中未找到 export default 对象`)
    return {}
  } catch (error) {
    console.warn(`解析文件 ${filePath} 失败:`, error.message)
    return {}
  }
}

// 处理语言目录
async function processLanguageDir(langDir, outputDir) {
  const files = fs.readdirSync(langDir)
  const translations = {}

  for (const file of files) {
    const filePath = path.join(langDir, file)
    const stat = fs.statSync(filePath)

    if (stat.isFile() && (file.endsWith('.ts') || file.endsWith('.js'))) {
      const fileName = path.basename(file, path.extname(file))

      try {
        const content = parseTsFile(filePath)

        // 如果是模块文件，使用文件名作为命名空间
        if (fileName !== 'index') {
          translations[fileName] = content
        } else {
          // 如果是 index 文件，直接合并
          Object.assign(translations, content)
        }
      } catch (error) {
        console.warn(`处理文件 ${filePath} 时出错:`, error.message)
      }
    }
  }

  // 扁平化翻译对象
  const flatTranslations = flattenObject(translations)

  // 写入 JSON 文件
  const outputFile = path.join(outputDir, `${path.basename(langDir)}.json`)
  fs.writeFileSync(outputFile, JSON.stringify(flatTranslations, null, 2), 'utf8')

  console.log(`生成扁平化翻译文件: ${outputFile}`)
  return flatTranslations
}

// 主函数
async function generateFlatI18n() {
  const localesDir = path.join(__dirname, '../src/locales/lang')
  const outputDir = path.join(__dirname, '../src/locales/flat')

  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  // 处理每个语言目录
  const langDirs = fs.readdirSync(localesDir).filter((item) => {
    const itemPath = path.join(localesDir, item)
    return fs.statSync(itemPath).isDirectory()
  })

  for (const langDir of langDirs) {
    const langPath = path.join(localesDir, langDir)
    await processLanguageDir(langPath, outputDir)
  }

  console.log('扁平化翻译文件生成完成！')
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  generateFlatI18n().catch(console.error)
}

export { generateFlatI18n, flattenObject }
