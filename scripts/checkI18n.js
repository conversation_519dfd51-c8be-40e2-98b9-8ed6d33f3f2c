/**
 * 检查 i18n 翻译完整性
 * 1. 检查各语言文件是否有缺失的翻译键
 * 2. 检查代码中使用的翻译键是否存在
 * 3. 检查是否有未使用的翻译键
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 递归获取对象的所有键
function getAllKeys(obj, prefix = '') {
  const keys = []

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const fullKey = prefix ? `${prefix}.${key}` : key

      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        keys.push(...getAllKeys(obj[key], fullKey))
      } else {
        keys.push(fullKey)
      }
    }
  }

  return keys
}

// 递归扫描目录中的文件
function scanDirectory(dir, extensions = ['.vue', '.ts', '.js']) {
  const files = []

  function scan(currentDir) {
    try {
      const items = fs.readdirSync(currentDir)

      for (const item of items) {
        const fullPath = path.join(currentDir, item)
        const stat = fs.statSync(fullPath)

        if (stat.isDirectory()) {
          // 跳过 node_modules 和其他不需要的目录
          if (!['node_modules', 'dist', 'build', '.git'].includes(item)) {
            scan(fullPath)
          }
        } else if (stat.isFile()) {
          const ext = path.extname(item)
          if (extensions.includes(ext)) {
            files.push(fullPath)
          }
        }
      }
    } catch (error) {
      console.warn(`扫描目录 ${currentDir} 失败:`, error.message)
    }
  }

  scan(dir)
  return files
}

// 加载语言文件
function loadLanguageFiles() {
  const localesDir = path.join(__dirname, '../src/locales/lang')
  const languages = {}

  try {
    const langDirs = fs.readdirSync(localesDir).filter((item) => {
      const itemPath = path.join(localesDir, item)
      return fs.statSync(itemPath).isDirectory()
    })

    for (const langDir of langDirs) {
      const langPath = path.join(localesDir, langDir)
      const files = fs.readdirSync(langPath)
      const translations = {}

      for (const file of files) {
        if (file.endsWith('.ts') && file !== 'index.ts') {
          const filePath = path.join(langPath, file)
          try {
            const content = fs.readFileSync(filePath, 'utf8')
            const fileName = path.basename(file, '.ts')
            translations[fileName] = `文件: ${filePath}`
          } catch (error) {
            console.warn(`读取文件 ${filePath} 失败:`, error.message)
          }
        }
      }

      languages[langDir] = translations
    }
  } catch (error) {
    console.warn(`加载语言文件失败:`, error.message)
  }

  return languages
}

// 扫描代码中使用的翻译键
function scanUsedKeys() {
  const usedKeys = new Set()
  const srcDir = path.join(__dirname, '..', 'src')

  // 扫描所有 Vue 和 TypeScript 文件
  const files = scanDirectory(srcDir, ['.vue', '.ts', '.js'])

  for (const filePath of files) {
    // 跳过翻译文件本身
    if (filePath.includes('locales/lang/')) continue

    try {
      const content = fs.readFileSync(filePath, 'utf8')

      // 匹配 t('key') 模式
      const matches = content.match(/\bt\s*\(\s*['"`]([^'"`]+)['"`]/g)
      if (matches) {
        matches.forEach((match) => {
          const keyMatch = match.match(/['"`]([^'"`]+)['"`]/)
          if (keyMatch) {
            usedKeys.add(keyMatch[1])
          }
        })
      }
    } catch (error) {
      console.warn(`读取文件 ${filePath} 失败:`, error.message)
    }
  }

  return Array.from(usedKeys)
}

// 主检查函数
function checkI18n() {
  console.log('🔍 开始检查 i18n 翻译完整性...\n')

  // 1. 加载语言文件
  console.log('📁 加载语言文件...')
  const languages = loadLanguageFiles()
  console.log(`发现语言: ${Object.keys(languages).join(', ')}\n`)

  // 2. 扫描使用的翻译键
  console.log('🔎 扫描代码中使用的翻译键...')
  const usedKeys = scanUsedKeys()
  console.log(`发现 ${usedKeys.length} 个使用的翻译键\n`)

  // 3. 显示使用的键
  console.log('📋 使用的翻译键:')
  usedKeys.sort().forEach((key) => {
    console.log(`  - ${key}`)
  })

  console.log('\n✅ i18n 检查完成!')
  console.log('\n💡 建议:')
  console.log('1. 手动检查各语言文件中是否包含所有使用的翻译键')
  console.log('2. 确保新增的翻译键在所有语言文件中都有对应的翻译')
  console.log('3. 定期清理未使用的翻译键')
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  try {
    checkI18n()
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error.message)
    process.exit(1)
  }
}

export { checkI18n, scanUsedKeys }
