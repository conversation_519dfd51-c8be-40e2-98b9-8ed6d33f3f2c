/**
 * 监听翻译文件变化，自动重新生成扁平化文件
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { generateFlatI18n } from './generateFlatI18n.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const localesDir = path.join(__dirname, '../src/locales/lang')

console.log('🔍 开始监听翻译文件变化...')
console.log(`监听目录: ${localesDir}`)

// 监听翻译文件目录
fs.watch(localesDir, { recursive: true }, (eventType, filename) => {
  if (filename && filename.endsWith('.ts')) {
    console.log(`📝 检测到文件变化: ${filename}`)
    console.log('🔄 重新生成扁平化文件...')
    
    generateFlatI18n()
      .then(() => {
        console.log('✅ 扁平化文件已更新')
      })
      .catch((error) => {
        console.error('❌ 生成扁平化文件失败:', error.message)
      })
  }
})

console.log('👀 监听已启动，按 Ctrl+C 停止监听')

// 优雅退出
process.on('SIGINT', () => {
  console.log('\n👋 停止监听翻译文件变化')
  process.exit(0)
})
