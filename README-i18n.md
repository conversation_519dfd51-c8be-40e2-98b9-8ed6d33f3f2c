# 🌐 i18n 国际化解决方案

## 问题背景

你的项目使用了自定义的 i18n 实现，传统的 i18n Ally 插件无法直接识别。我们提供了多种解决方案来改善国际化开发体验。

## ✅ 已完成的配置

### 1. i18n Ally 插件配置

- ✅ 添加到 VSCode 推荐扩展列表
- ✅ 配置 `.i18nrc.json` 专门适配项目结构
- ✅ 优化 VSCode 设置中的 i18n Ally 配置

### 2. 开发工具脚本

- ✅ `pnpm run i18n:check` - 检查翻译完整性
- ✅ `pnpm run i18n:flat` - 生成扁平化文件
- ✅ VSCode 任务集成

### 3. 可视化管理工具

- ✅ `tools/i18n-manager.html` - 浏览器中的翻译管理界面

## 🚀 使用方法

### 方法一：使用 i18n Ally 插件（推荐）

1. **安装插件**

   ```
   在 VSCode 扩展市场搜索 "i18n Ally" 并安装
   ```

2. **生成扁平化文件**

   ```bash
   pnpm run i18n:flat
   ```

3. **重启 VSCode**

   ```
   让配置生效
   ```

4. **验证是否工作**

   - 打开 `src/views/test-i18n-ally.vue` 文件
   - 查看翻译键是否有内联显示和悬停提示
   - 不存在的键应该会被标记为错误

5. **开启自动监听（可选）**
   ```bash
   pnpm run i18n:watch
   ```
   这会监听翻译文件变化并自动重新生成扁平化文件

### 方法二：使用自定义工具

1. **检查翻译完整性**

   ```bash
   pnpm run i18n:check
   ```

   这会扫描所有代码文件，找出：

   - 使用的翻译键（共 1797 个）
   - 各语言文件的状态
   - 缺失的翻译

2. **生成扁平化文件**

   ```bash
   pnpm run i18n:flat
   ```

   生成的文件位于 `src/locales/flat/` 目录

3. **监听文件变化**

   ```bash
   pnpm run i18n:watch
   ```

   自动监听翻译文件变化并重新生成扁平化文件

4. **使用可视化工具**
   ```
   打开 tools/i18n-manager.html 文件
   ```

### 方法三：使用 VSCode 任务

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 "Tasks: Run Task"
3. 选择：
   - "生成扁平化 i18n 文件"
   - "检查 i18n 翻译完整性"
   - "监听翻译文件变化"

## 📊 项目 i18n 统计

根据最新扫描结果：

- **支持语言**: 2 个（zh-CN, en）
- **使用的翻译键**: 1797 个
- **翻译文件**: 约 90+ 个模块文件

## 🔧 项目特点

你的项目有以下特殊的 i18n 实现：

1. **自定义 useI18n Hook**

   ```typescript
   import { useI18n } from '@/hooks/web/useI18n'
   const { t } = useI18n()
   ```

2. **嵌套文件结构**

   ```
   src/locales/lang/
   ├── zh-CN/
   │   ├── common.ts
   │   ├── system.ts
   │   └── ...
   └── en/
       ├── common.ts
       └── ...
   ```

3. **命名空间支持**
   ```typescript
   const { t } = useI18n('common')
   t('saveText') // 等同于 t('common.saveText')
   ```

## 💡 开发建议

### 添加新翻译的工作流

1. **在中文文件中添加翻译**

   ```typescript
   // src/locales/lang/zh-CN/common.ts
   export default {
     newFeature: '新功能',
   }
   ```

2. **在英文文件中添加对应翻译**

   ```typescript
   // src/locales/lang/en/common.ts
   export default {
     newFeature: 'New Feature',
   }
   ```

3. **在代码中使用**

   ```vue
   <template>
     <div>{{ t('common.newFeature') }}</div>
   </template>
   ```

4. **运行检查验证**
   ```bash
   pnpm run i18n:check
   ```

### 最佳实践

- ✅ 使用描述性的键名
- ✅ 保持所有语言文件的键结构一致
- ✅ 定期运行 `i18n:check` 检查完整性
- ✅ 避免在代码中硬编码文本
- ✅ 使用命名空间组织相关翻译

## 🔍 故障排除

### i18n Ally 不工作？

1. 确认插件已安装并启用
2. 重启 VSCode
3. 检查 VSCode 输出面板中的 "i18n Ally" 日志
4. 使用自定义工具作为替代方案

### 翻译不显示？

1. 检查键名是否正确
2. 确认翻译文件中存在对应的键
3. 重启开发服务器

## 📚 相关文件

- `.i18nrc.json` - i18n Ally 配置
- `.vscode/settings.json` - VSCode 设置
- `scripts/checkI18n.js` - 翻译检查脚本
- `scripts/generateFlatI18n.js` - 扁平化生成脚本
- `tools/i18n-manager.html` - 可视化管理工具
- `docs/i18n-guide.md` - 详细开发指南

---

**总结**: 现在你有多种方式来管理项目的国际化翻译，从 VSCode 插件到自定义脚本，再到可视化工具，可以根据需要选择最适合的方案。
