{"version": "2.0.0", "tasks": [{"type": "npm", "script": "dev", "label": "pnpm: dev", "detail": "开发环境", "problemMatcher": ["$vite"], "isBackground": true}, {"type": "npm", "script": "build", "label": "pnpm: build", "detail": "生产环境打包"}, {"type": "npm", "script": "build:test", "label": "pnpm: build:test", "detail": "测试环境打包"}, {"type": "npm", "script": "lint:eslint", "label": "pnpm: lint:eslint", "detail": "ESLint 代码检查"}, {"type": "npm", "script": "lint:prettier", "label": "pnpm: lint:prettier", "detail": "Prettier 代码格式化"}, {"type": "npm", "script": "update:iconfont", "label": "pnpm: update:iconfont", "detail": "更新项目图标字体"}, {"label": "生成扁平化 i18n 文件", "type": "shell", "command": "node", "args": ["scripts/generateFlatI18n.js"], "group": "build", "detail": "为 i18n Ally 插件生成扁平化翻译文件", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "检查 i18n 翻译完整性", "type": "shell", "command": "node", "args": ["scripts/checkI18n.js"], "group": "test", "detail": "检查各语言翻译文件的完整性", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "监听翻译文件变化", "type": "shell", "command": "node", "args": ["scripts/watchI18n.js"], "group": "build", "detail": "监听翻译文件变化并自动重新生成扁平化文件", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": [], "isBackground": true}]}