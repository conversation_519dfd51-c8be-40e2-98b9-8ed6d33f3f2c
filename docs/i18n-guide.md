# 国际化 (i18n) 开发指南

## 概述

本项目使用自定义的 i18n 实现，基于 Vue I18n，但有以下特点：
- 自封装的 `useI18n` hook
- 动态文件加载机制
- 嵌套的文件结构

## 项目 i18n 结构

```
src/locales/
├── index.ts              # i18n 实例配置
├── helper.ts             # 辅助函数
├── useLocale.ts          # 语言切换 hook
└── lang/                 # 语言文件目录
    ├── zh-CN/            # 中文翻译
    │   ├── common.ts     # 通用翻译
    │   ├── system.ts     # 系统翻译
    │   └── ...
    ├── en/               # 英文翻译
    │   ├── common.ts
    │   └── ...
    ├── zh_CN.ts          # 中文语言包入口
    └── en.ts             # 英文语言包入口
```

## 使用方式

### 1. 在组件中使用翻译

```vue
<template>
  <div>
    <h1>{{ t('common.welcomeText') }}</h1>
    <button>{{ t('common.saveText') }}</button>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
</script>
```

### 2. 使用命名空间

```typescript
import { useI18n } from '@/hooks/web/useI18n'

// 使用 common 命名空间
const { t } = useI18n('common')

// 现在可以直接使用 saveText 而不需要 common.saveText
console.log(t('saveText')) // 等同于 t('common.saveText')
```

## i18n Ally 插件配置

由于项目使用了自定义的 i18n 实现，i18n Ally 插件可能无法完全自动识别。我们提供了以下解决方案：

### 方案一：使用配置好的 i18n Ally

1. 安装 i18n Ally 插件
2. 重启 VSCode
3. 插件会根据 `.i18nrc.json` 和 VSCode 设置自动配置

### 方案二：使用自定义工具

#### 1. 生成扁平化翻译文件

```bash
# 运行脚本生成扁平化文件
pnpm run i18n:flat

# 或者使用 VSCode 任务
# Ctrl+Shift+P -> Tasks: Run Task -> 生成扁平化 i18n 文件
```

#### 2. 检查翻译完整性

```bash
# 检查翻译文件完整性
pnpm run i18n:check

# 或者使用 VSCode 任务
# Ctrl+Shift+P -> Tasks: Run Task -> 检查 i18n 翻译完整性
```

#### 3. 使用可视化管理工具

打开 `tools/i18n-manager.html` 文件，在浏览器中查看翻译管理界面。

## 开发工作流

### 添加新的翻译键

1. **在对应的语言文件中添加翻译**
   ```typescript
   // src/locales/lang/zh-CN/common.ts
   export default {
     // 现有翻译...
     newFeatureTitle: '新功能标题',
     newFeatureDesc: '新功能描述'
   }
   ```

2. **在英文文件中添加对应翻译**
   ```typescript
   // src/locales/lang/en/common.ts
   export default {
     // 现有翻译...
     newFeatureTitle: 'New Feature Title',
     newFeatureDesc: 'New Feature Description'
   }
   ```

3. **在代码中使用**
   ```vue
   <template>
     <div>
       <h2>{{ t('common.newFeatureTitle') }}</h2>
       <p>{{ t('common.newFeatureDesc') }}</p>
     </div>
   </template>
   ```

4. **运行检查脚本验证**
   ```bash
   pnpm run i18n:check
   ```

### 最佳实践

1. **保持键名一致**：确保所有语言文件中的键名结构相同
2. **使用描述性键名**：使用有意义的键名，如 `user.profile.editButton` 而不是 `btn1`
3. **定期检查**：使用提供的工具定期检查翻译完整性
4. **避免硬编码**：不要在代码中直接写文本，始终使用翻译键

## 故障排除

### i18n Ally 插件不工作

1. **检查插件是否安装并启用**
2. **重启 VSCode**
3. **检查配置文件**：确保 `.i18nrc.json` 和 VSCode 设置正确
4. **查看输出面板**：在 VSCode 输出面板中选择 "i18n Ally" 查看错误信息

### 翻译不显示

1. **检查键名是否正确**：确保翻译键在对应语言文件中存在
2. **检查文件导出**：确保翻译文件正确导出 default 对象
3. **检查语言设置**：确认当前语言设置正确

### 新增翻译不生效

1. **重启开发服务器**：`pnpm run dev`
2. **清除缓存**：`pnpm run clean:cache`
3. **检查文件路径**：确保新文件在正确的目录结构中

## 工具说明

- **scripts/generateFlatI18n.js**：生成扁平化翻译文件，便于 i18n Ally 识别
- **scripts/checkI18n.js**：检查翻译完整性和使用情况
- **tools/i18n-manager.html**：可视化翻译管理工具
- **.i18nrc.json**：i18n Ally 插件配置文件
