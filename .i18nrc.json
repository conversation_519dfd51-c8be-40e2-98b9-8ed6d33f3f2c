{"localesPaths": ["src/locales/flat"], "keystyle": "flat", "sortKeys": true, "namespace": false, "enabledParsers": ["json"], "sourceLanguage": "zh-CN", "displayLanguage": "zh-CN", "enabledFrameworks": ["vue", "vue-i18n"], "pathMatcher": "{locale}.json", "extract": {"keygenStyle": "camelCase"}, "annotations": true, "autoDetection": false, "dirStructure": "file", "fullReloadOnChanged": true, "tabStyle": "tab", "usage": {"scanningIgnore": ["node_modules/**", "dist/**", "build/**", "scripts/**", "tools/**", "src/locales/lang/**"]}, "regex": {"key": "(?:['\"`])([^'\"`]+)(?:['\"`])", "usageMatchAppend": ["\\bt\\s*\\(\\s*['\"`]({key})['\"`]", "\\$t\\s*\\(\\s*['\"`]({key})['\"`]"]}, "refactor": {"templates": [{"name": "vue-i18n", "patterns": ["**/*.vue", "**/*.ts", "**/*.js"]}]}, "review": {"enabled": true}, "editor": {"preferEditor": true}}