import { Alova } from '@/utils/http/alova/index'

interface Permission {
  id: number
}

export interface Role {
  id: number
  role_name: string
  identify: string
  parent_id: number
  description: string | null
  data_range: number
  created_at: string
  updated_at: string
  check_public_lead_permission: number
  public_lead_permission: null | unknown
  creator: string
  children?: Role[]
  permissions?: Permission[]
}

export interface Action {
  id: number
  parent_id: number
  permission_name: string
  route?: string
  icon?: string
  module: string
  permission_mark: string
  component?: string
  redirect?: string
  keepalive: number
  type: number
  hidden: number
  sort: number
  active_menu?: string
  creator_id?: number
  created_at: string
  updated_at: string
}

// 定义权限项接口
export interface RolePermission {
  id: number
  parent_id: number
  permission_name: string
  route: string
  icon: string
  module: string
  permission_mark: string
  component: string
  redirect?: string
  keepalive: number
  type: number
  hidden: number
  active_menu?: string
  sort: number
  created_at: string
  updated_at: string
  creator?: string
  children?: Permission[]
  actions?: Action[]
}

/**
 * @description: 角色列表
 */
export function getRoleList(params?: any) {
  return Alova.Get<Role[]>(`/permissions/roles`, { params })
}

/**
 * @description: 创建角色
 */
export function createRole(params) {
  return Alova.Post(`/permissions/roles`, params)
}

/**
 * @description: 更新角色
 */
export function updateRole(id: number, params: any) {
  return Alova.Put(`/permissions/roles/${id}`, params)
}

/**
 * @description: 删除角色
 */
export function deleteRole(id: number) {
  return Alova.Delete(`/permissions/roles/${id}`)
}

/**
 * @description: 功能权限列表
 */
export function getRolePermissions() {
  return Alova.Get<RolePermission>(`/permissions/permissions?from=role`)
}

/**
 * @description: 数据权限
 */
export function getDataPermissions() {
  return Alova.Get(`/options/dataRange`)
}
