import { Alova } from '@/utils/http/alova/index'

interface Role {
  id: number
  role_name: string
  identify: string
  parent_id: number
  description: string
  data_range: number
  check_public_lead_permission: number
  public_lead_permission: any
  creator_id: number
  created_at: string
  updated_at: string
  member_count: number
  pivot: {
    user_id: number
    role_id: number
  }
}

interface Job {
  id: number
  job_name: string
  coding: any // 根据实际情况定义类型
  status: number
  sort: number
  description: any // 根据实际情况定义类型
  creator_id: number
  created_at: string
  updated_at: string
  pivot: {
    user_id: number
    job_id: number
  }
}

export interface User {
  id: number
  username: string
  email: string
  avatar: string | null
  creator_id: number
  status: number
  department_id: number
  created_at: string
  phone: string
  employee_name: string
  is_principal: number
  remark: string
  creator: string
  lead_daily_receive_num: any
  lead_pool_limit: any
  department_link: string
  roles: Role[]
  jobs: Job[]
  department_tree: string
}

/**
 * @description: 角色列表
 */
export function getUserList(params: IPageRequest) {
  return Alova.Get<IPageResult<User>>(`/users`, { params })
}

/**
 * @description: 新增员工
 */
export function createUser(params) {
  return Alova.Post(`/users`, params)
}

/**
 * @description: 编辑员工数据
 */
export function updateUser(id: number, params: any) {
  return Alova.Put(`/users/${id}`, params)
}

/**
 * @description: 删除角色
 */
export function deleteRole(id: number) {
  return Alova.Delete(`/permissions/roles/${id}`)
}

/**
 * @description: 线索设置
 */
export function setLeads(params: any) {
  return Alova.Post(`/user/set_leads`, params)
}

/**
 * @description: 重置密码
 */
export function resetPSW(params: any) {
  return Alova.Post(`/user/reset_password`, params)
}

/**
 * @description: 重置密码
 */
export function setRole(params: any) {
  return Alova.Post(`/user/set_role`, params)
}

/**
 * @description: 检查是否可以设置为部门负责人
 */
export function checkPrincipal(params: any) {
  return Alova.Post(`/user/checkPrincipal`, params)
}
