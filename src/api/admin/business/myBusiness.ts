import { Alova } from '@/utils/http/alova/index'
import {
  RequestGetOpportunityList,
  BusinessList,
  RequestCollaboratorListParams,
  CollaboratorList,
  RequestGetCustomerList,
  CustomerList,
} from './type'
/**
 * 查询商机列表
 * @param params
 * @returns
 */
export function getOpportunityList(params: RequestGetOpportunityList) {
  return Alova.Get<BusinessList[]>('/businessOpportunities/opportunity', { params })
}

/**
 * 获取商机协作员列表
 */
export function getCollaboratorList(params: RequestCollaboratorListParams) {
  return Alova.Get<CollaboratorList[]>('/businessOpportunities/collaborator', { params })
}

/**
 * @description: 获取客户列表
 */
export function getCustomerList(params: RequestGetCustomerList) {
  return Alova.Get<IPageResult<CustomerList>>('/customer/getCustomerPageList', { params })
}
