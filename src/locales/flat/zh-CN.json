{"agreement.agreementName": "协议名称", "agreement.addAgreement": "新增协议", "agreement.agreementEdit": "编辑协议", "agreement.enterAgreementName": "请输入协议名称", "agreement.enterAgreementContent": "请输入协议内容", "agreement.confirmDeleteData": "删除该条数据后不可以恢复,是否继续?", "agreement.agreeName": "名称", "agreement.warningText": "警告", "agreement.enterNotes": "请输入备注", "agreement.agreementContent": "协议内容", "agreement.enterYourContent": "输入您的内容吧！", "agreement.creationTime": "创建时间", "agreement.searchAgreement": "协议搜索", "agreement.enterAgreementSearch": "请输入协议搜索", "application.list.titlePlaceholder": "请输入标题", "application.list.groupPlaceholder": "请选择应用分组", "application.list.applyName": "应用名称", "application.list.updateConfig": "更新配置", "application.list.updateConfigConfirmContent": "确认更新配置吗？", "application.list.title": "应用列表", "application.list.group": "应用分组", "application.list.appNameVersion": "应用名/版本信息", "application.list.appIdentifier": "应用标识", "application.list.author": "作者", "application.list.synopsis": "简介", "application.list.isPublished": "是否上架", "application.list.applyTitle": "应用标题", "application.list.applyTitlePlaceholder": "请输入应用标题", "application.list.publishConfirm": "上架后商城用户可在应用市场安装此插件，确认上架吗?", "application.list.unpublishConfirm": "下架应用后商城用户无法在应用市场安装此插件，确认下架吗?", "application.list.uninstallConfirm": "确定要卸载此应用吗？此操作不可逆，请谨慎考虑。", "application.list.uninstallText": "卸载", "application.list.uninstallSuccess": "卸载成功", "application.list.installConfirm": "确定要安装该应用吗？", "application.list.pleaseSelectApp": "请先选择应用", "application.list.updateConfigSuccess": "更新配置成功", "application.list.icon": "应用图标", "application.list.introduction": "应用简介", "application.list.introductionPlaceholder": "请输入应用简介", "application.list.content": "应用内容", "application.list.editorPlaceholder": "请输入富文本内容", "application.list.editorDescription": "详细介绍插件的功能和使用方法", "application.list.editApply": "编辑应用", "application.list.warning": "警告", "application.list.batchInstall": "批量安装", "application.list.install": "安装", "application.paySet.applicationSearch": "应用搜索", "application.paySet.applicationSearchPlaceholder": "请输入应用名称", "application.paySet.noPaySetting": "未设置付费", "application.paySet.setPaySetting": "设置", "application.paySet.noData": "暂无数据", "application.paySet.permanent": "永久", "application.paySet.month": "月", "application.paySet.year": "年", "application.paySet.yuan": "元", "application.paySet.applicationName": "应用名称", "application.paySet.paymentMethod": "支付方式", "application.paySet.setPermanentPrice": "设置永久价格", "application.paySet.setPrice": "设置售价", "application.paySet.submit": "提交", "application.paySet.delete": "删除", "application.paySet.addPrice": "添加售价", "application.paySet.inputPrice": "输入价格", "application.paySet.maxAddPrice": "最多可添加3条", "application.paySet.addPriceText": "添加售价", "application.paySet.yes": "是", "application.paySet.no": "否", "application.paySet.wechatPay": "微信支付", "application.paySet.alipayPay": "支付宝支付", "application.paySet.paymentTypeRequired": "请选择支付方式", "application.paySet.pleaseInputUnit": "请输入单位", "application.paySet.pleaseInputPrice": "请输入价格", "application.paySet.modifySuccess": "设置成功", "apply.applicationSearch": "应用搜索", "apply.applicationSearchPlaceholder": "请输入应用名称", "apply.title": "提示", "apply.toggleDown": "是否把该应用取消置顶?", "apply.toggleUp": "是否把该应用置顶?", "apply.positiveText": "确定", "apply.negativeText": "取消", "apply.useCourse": "使用教程", "apply.lookDetails": "查看详情", "apply.upText": "已置顶成功", "apply.closeUpText": "已取消置顶", "apply.expireTime": "到期时间", "apply.createTime": "创建时间", "apply.successTime": "完成时间", "apply.buy": "购买", "apply.renew": "续费", "apply.unload": "卸载", "apply.download": "安装", "apply.periodTime": "有效期", "apply.applyDetails": "应用详情", "apply.priceName": "价格", "apply.addBuyText": "提交订单", "apply.orderBuyText": "订单支付", "apply.orderResult": "订单结果", "apply.buyShop": "购买店铺", "apply.readText": "已阅读并同意", "apply.agreement": "《服务协议》", "apply.actualPayment": "实际付款", "apply.orderComplete": "订单完成", "apply.orderCompletes": "订单已完成", "apply.obligation": "待付款", "apply.completed": "已完成", "apply.closed": "已关闭", "apply.toMeApply": "前往我的应用", "apply.alreadyText": "前往已购应用", "apply.immediatelyPayment": "马上付款", "apply.toBuyText": "你可前往【购买记录】查看应用信息", "apply.buyList": "购买记录", "apply.leaveFor": "你可前往", "apply.lookInfo": "查看应用信息", "apply.orderClosed": "订单已关闭", "apply.orderNo": "订单编号", "apply.stopText": "取消订单", "apply.toPay": "立即付款", "apply.payTextDesc": "请在{time}前付款，避免订单超时取消", "apply.orderMsg": "订单信息", "apply.agreementText": "请先阅读并同意服务协议", "apply.serveText": "服务协议", "apply.payMode": "支付方式", "apply.payMoney": "支付金额", "apply.doneOrder": "已完成支付", "apply.wxCodePay": "请使用微信扫码支付", "apply.applyName": "应用名称", "apply.applyPeriod": "应用周期", "apply.applyPeriodText": "周期", "apply.bookShop": "订购店铺", "apply.orderText": "订单", "apply.closeOrderText": "是否取消订单？", "apply.warningText": "警告", "apply.titleText": "提示", "apply.uninstallText": "即将卸载该应用, 是否继续?", "apply.uninstallSuccess": "卸载成功", "apply.installSuccess": "安装成功", "apply.uninstallFail": "卸载失败", "apply.uninstalling": "卸载中", "apply.uninstalled": "已卸载", "apply.uninstallingText": "正在卸载中，请稍等", "apply.uninstalledText": "已卸载", "apply.uninstallingFail": "卸载失败", "apply.uninstallingSuccess": "卸载成功", "apply.closeOrder": "取消订单成功", "apply.perpetual": "期限：永久有效", "apply.operatorAccount": "操作账号", "apply.applicationInfo": "应用信息", "apply.perpetualText": "永久有效", "apply.actualPaymentText": "实付", "apply.transactionStatus": "交易状态", "apply.totalPrice": "总价", "apply.unitPrice": "单价", "apply.noApplicationIntroduction": "暂无应用简介", "auth.role.roleManagement": "角色权限管理", "auth.role.addRole": "添加角色", "auth.role.menuPermission": "菜单权限", "auth.role.assignMenuPermission": "分配 {0} 的菜单权限", "auth.role.confirmDelete": "你确定要删除该角色吗？", "auth.role.deleteSuccess": "删除成功", "auth.role.deleteFailed": "删除失败", "auth.role.searchPlaceholder": "请输入菜单名称搜索", "auth.role.selectAll": "全选", "auth.role.expandAll": "全部展开", "auth.role.collapseAll": "全部收起", "auth.role.getRolePermissionFailed": "获取角色权限失败", "auth.role.id": "id", "auth.role.roleName": "角色名称", "auth.role.roleNameRequired": "请输入角色名称", "auth.role.roleNameNotNumber": "角色名称不能为纯数字", "auth.role.remarkNotNumber": "备注不能为纯数字", "auth.role.roleNamePlaceholder": "请输入角色名称/角色ID", "common.fullScreenText": "全屏", "common.restoreText": "还原", "common.languageText": "语言", "common.lockScreenText": "锁屏", "common.systemConfigText": "系统配置", "common.switchDarkText": "切换深色主题", "common.switchLightText": "切换浅色主题", "common.warningText": "警告", "common.tipsText": "提示", "common.tipsWarningText": "温馨提示", "common.tipsKnow": "知道了", "common.okText": "确认", "common.closeText": "关闭", "common.loadingText": "加载中...", "common.searchText": "搜索", "common.queryText": "查询", "common.detailText": "详情", "common.createText": "新增", "common.editText": "编辑", "common.completeText": "完成", "common.copyText": "复制", "common.previewText": "预览", "common.createTimeText": "创建时间", "common.updateTimeText": "更新时间", "common.sortText": "排序", "common.actionText": "操作", "common.enableText": "启用", "common.disableText": "禁用", "common.moreText": "更多", "common.inputText": "请输入", "common.chooseText": "请选择", "common.remarkText": "备注", "common.statusText": "状态", "common.copySuccess": "复制成功", "common.copyFailed": "复制失败", "common.saveSuccess": "保存成功", "common.setSuccess": "设置成功", "common.setFailed": "设置失败", "common.batchImport": "批量导入", "common.emptyText": "暂无数据", "common.taskSubmitted": "任务已提交，请在下载中心查看", "common.redo": "刷新", "common.back": "返回", "common.light": "亮色主题", "common.dark": "黑暗主题", "common.userCenter": "个人中心", "common.downloadCenter": "下载中心", "common.userSetting": "个人设置", "common.editPassword": "修改密码", "common.password": "密码", "common.logout": "退出登录", "common.logoutTip": "您确定要退出登录吗", "common.logoutSuccess": "成功退出登录", "common.resetPSWSuccess": "密码已经修改，请重新登录", "common.i18nText": "多语言", "common.welcomeText": "你好，欢迎了解更多产品", "common.addSuccess": "新增成功", "common.editSuccess": "编辑成功", "common.deleteSuccess": "删除成功", "common.operationSuccess": "操作成功", "common.operationFailed": "操作失败", "common.updateSuccess": "编辑成功", "common.exportSuccess": "导出成功,请到下载中心查看", "common.importSuccess": "导入成功", "common.confirmContent": "是否确认操作？", "common.deleteConfirmTitle": "删除确认", "common.deleteConfirmContent": "是否确认删除该记录？", "common.cancelText": "取消", "common.confirmText": "确定", "common.reminderText": "温馨提示", "common.batchDelete": "批量删除", "common.batchEnable": "批量启用", "common.batchDisable": "批量禁用", "common.batchDeleteTip": "确定要删除选中的 {0} 条记录吗？", "common.selectAtLeastOneRecord": "请至少选择一条记录", "common.submitText": "提交", "common.saveText": "保存", "common.resetText": "重置", "common.clearText": "清除", "common.deleteText": "删除", "common.sortTip": "排序数值需要大于等于0，且数值越小，在前端的展示越靠前", "common.formValidateFailed": "表单校验失败", "common.checkForm": "请检查表单填写是否正确", "common.confirmBack": "退回", "components.colorPicker.vertical": "上下渐变", "components.colorPicker.horizontal": "左右渐变", "curd.template.dialogAdd": "弹窗新增", "curd.template.drawerAdd": "抽屉新增", "curd.template.addAppointment": "新增预约", "curd.template.editAppointment": "编辑预约", "curd.template.batchDelete": "批量删除", "curd.template.selectAtLeastOneRecord": "请至少选择一条记录", "curd.template.confirmDeleteRecords": "确定要删除选中的 {0} 条记录吗？", "curd.template.clickedButton": "您点击了，{0} 按钮", "curd.template.submitSuccess": "提交成功", "curd.template.id": "id", "curd.template.name": "名称", "curd.template.avatar": "头像", "curd.template.gender": "性别", "curd.template.status": "状态", "curd.template.email": "邮箱", "curd.template.city": "城市", "curd.template.male": "男", "curd.template.female": "女", "curd.template.unknown": "未知", "curd.template.nameLabel": "姓名", "curd.template.namePlaceholder": "请输入姓名", "curd.template.nameTooltip": "这是一个提示", "curd.template.nameRequired": "请输入姓名", "curd.template.mobileLabel": "手机", "curd.template.mobilePlaceholder": "请输入手机号码", "curd.template.mobileRequired": "请输入手机号码", "curd.template.typeLabel": "类型", "curd.template.typePlaceholder": "请选择类型", "curd.template.typeRequired": "请选择类型", "curd.template.typeComfort": "舒适性", "curd.template.typeEconomy": "经济性", "curd.template.appointmentTimeLabel": "预约时间", "curd.template.appointmentTimeRequired": "请选择预约时间", "curd.template.stayTimeLabel": "停留时间", "curd.template.stayTimeRequired": "请选择停留时间", "curd.template.sourceLabel": "来源", "curd.template.sourcePlaceholder": "请输入来源", "curd.template.sourceRequired": "请输入来源", "curd.template.addressLabel": "地区", "curd.template.addressPlaceholder": "请输入地区", "curd.template.addressRequired": "请输入地区", "curd.template.keywordLabel": "用户信息", "curd.template.keywordPlaceholder": "请输入昵称/账号/ID", "curd.template.statusLabel": "状态", "curd.template.statusPlaceholder": "请选择状态", "curd.template.statusAll": "全部", "curd.template.statusEnabled": "启用", "curd.template.statusDisabled": "禁用", "curd.template.createDateLabel": "创建时间", "curd.template.today": "今天", "curd.template.yesterday": "昨天", "curd.template.lastWeek": "最近一周", "curd.template.lastMonth": "最近一个月", "curd.template.friendIntroduction": "朋友介绍", "customer.common.customerName": "联系人姓名", "customer.common.customerSex": "联系人性别", "customer.common.customerAge": "联系人年龄", "customer.common.customerNamePlaceholder": "请输入联系人名称", "customer.common.customerNameRequired": "请输入不超过30个字的联系人姓名！", "customer.common.customerAgePlaceholder": "请输入联系人年龄", "customer.common.customerAgeRequired": "年龄只能是两位数以内纯数字！", "customer.common.companyName": "企业名称", "customer.common.companyNamePlaceholder": "请输入公司名称", "customer.common.companyNameRequired": "请输入不超过30个字的联系人姓名！", "customer.common.customerPhone": "联系电话", "customer.common.customerPhonePlaceholder": "请输入联系电话", "customer.common.customerPhoneRequired": "请输入正确的联系电话！", "customer.common.customerWechat": "联系人微信", "customer.common.customerWechatPlaceholder": "请输入线索联系人微信", "customer.common.wechatRequired": "微信号不能超过30位！", "customer.common.industry": "所属行业", "customer.common.selectPlaceholder": "不限", "customer.common.transactionStatus": "成交状态", "customer.common.customerLabel": "客户标签", "customer.common.transactionMethod": "转化方式", "customer.common.addTime": "添加时间", "customer.common.addCustomer": "新增客户", "customer.common.importCustomer": "导入客户", "customer.common.exportCustomer": "导出客户", "customer.common.exportInfo": "导出信息", "customer.common.exportRequired": "请选择导出信息", "customer.common.moveCustomer": "转移客户", "customer.common.returnLeads": "退回线索", "customer.common.customer": "联系人", "customer.common.businessNumber": "商机数量", "customer.common.principal": "负责人", "customer.common.transformationMethod": "转化方式", "customer.common.otherInfo": "其他信息", "customer.common.allCustomer": "全部", "customer.common.myDuty": "我负责的", "customer.common.myCooperation": "我协作的", "customer.common.settled": "已成交", "customer.common.unsettled": "未成交", "customer.common.remark": "备注", "customer.common.remarkRequired": "备注不能超过100个字！", "customer.common.location": "所在地", "customer.common.address": "详细地址", "customer.common.addressPlaceholder": "详细地址不能超过50个字！", "customer.common.addressRequired": "请输入详细地址！", "customer.common.source": "渠道来源", "customer.common.sourceRequired": "请选择渠道来源！", "customer.common.sourcePlaceholder": "请选择", "customer.common.detail": "详情", "customer.common.customerDetail": "客户详情", "customer.common.collaboratorName": "姓名", "customer.common.collaboratorAccount": "账号", "customer.common.collaboratorDepartment": "部门", "customer.common.collaboratorRole": "角色", "customer.common.collaboratorPermission": "商机权限", "customer.common.addCollaborator": "添加协作员", "customer.common.selectCollaborator": "选择协作员", "customer.common.searchPlaceholder": "请输入搜索内容", "customer.common.pleaseSelectCollaborator": "请选择协作员", "customer.common.dataPermission": "数据权限", "customer.common.addTag": "添加标签", "customer.common.selectedTags": "已选标签", "customer.common.tagManagement": "标签管理", "customer.common.clear": "清空", "customer.common.unit": "个", "customer.common.backToSea": "退回线索", "customer.common.addBusiness": "新增商机", "customer.common.customerOverview": "客户概况", "customer.common.detailCustomerName": "客户名称", "customer.common.intentionProduct": "意向产品", "customer.common.basicInformation": "基本信息", "customer.common.entryMethods": "转化方式", "customer.common.createdAt": "创建时间", "customer.common.currentPosition": "当前位置", "customer.common.department": "所属部门", "customer.common.assignTime": "分配时间", "customer.common.collaborator": "协作员", "customer.common.assigner": "分配人", "customer.common.updatedAt": "最新操作时间", "customer.common.creator": "创建人", "customer.common.firstAssignTime": "首次分配时间", "customer.common.callStatus": "通话状态", "customer.common.followStatus": "跟进状态", "customer.common.director": "负责人", "customer.common.customerReturn": "退回客户", "customer.common.returnReason": "退回原因", "customer.common.returnReasonRequired": "请输入不超过50字的退回原因！", "customer.common.customerTransfer": "客户转移", "customer.common.customerTransferReason": "请输入转移原因", "customer.common.editCustomer": "编辑客户", "customer.common.exportCurrentList": "导出当前列表", "customer.common.pleaseInputEmployeeName": "请输入协作员姓名", "customer.common.pleaseInputFileName": "请输入文件名", "customer.common.fileNameError": "请输入不超过20个字的文件名！", "customer.common.businessName": "商机名称", "customer.common.businessNamePlaceholder": "请输入商机名称", "customer.common.businessNameRequired": "请输入不超过30个字的商机名称！", "customer.common.businessStage": "商机阶段", "customer.common.estimatedWinAmount": "预计成交金额", "customer.common.estimatedWinRate": "预计赢单率", "customer.common.estimatedWinDate": "预计成交日期", "customer.common.sourceAccount": "渠道来源账号", "customer.common.sourceAccountPlaceholder": "请输入渠道账号", "customer.common.sourceAccountRequired": "请选择渠道来源账号！", "customer.common.pleaseSelectSource": "请选择", "customer.common.selectDirector": "请选择负责人", "customer.common.addUpRecord": "写跟进", "customer.common.addFollowRecord": "添加跟进记录", "customer.common.followRecord": "跟进记录", "customer.common.followTime": "跟进时间", "customer.common.productInfo": "产品信息", "customer.common.pleaseInputFollowRecord": "请输入跟进记录", "customer.common.pleaseInputFollowTime": "请选择跟进时间", "customer.common.pleaseInputFollowRecordRequired": "请输入跟进记录", "customer.common.pleaseInputFollowTimeRequired": "请选择跟进时间", "customer.common.pleaseInputFollowRecordMax": "最多输入200字", "customer.common.pleaseInputSearchKeyword": "请输入搜索关键字", "deal.orderCfg": "订单设置", "deal.afterSaleCfg": "售后设置", "deal.enterNick": "请输入昵称", "deal.enterPhone": "请输入电话号码", "deal.enterValidPhone": "请输入有效的11位电话号码", "deal.enterAddr": "请输入地址", "deal.enterReason": "请输入原因", "deal.enterCorrectNum": "请输入正确的数字", "deal.enterNumGtZero": "请输入大于0的数字", "deal.recipientName": "退货收货人姓名", "deal.returnName": "请输入退货收货人姓名", "deal.returnPhone": "退货收货人电话", "deal.returnAddr": "退货收货人地址", "deal.returnReason": "退货理由", "deal.enterReturnReason": "配置退货理由，一行一个理由", "deal.enterReturnRecipientPhone": "请输入退货收货人电话", "deal.enterReturnRecipientAddress": "请输入退货收货人地址", "deal.enterReturnReasonTips": "请输入退货理由", "deal.orderAutoCloseTime": "订单自动关闭时间（分钟）", "deal.orderSubmissionPendingPayDuration": "订单提交待支付时长，0为默认15分钟", "deal.orderAutoReceiveTime": "订单自动收货时间（天）", "deal.orderAutoReceiveTimeDescription": "订单自动收货时间是自发货日算起，例如:设置10天，表示自发货日起10天系统会自动收货;默认为0时，表示自动收货时间是15天", "deal.afterSaleDuration": "售后时长", "deal.afterSaleDurationDescription": "例如:设置10天指用户确认收货10天内可以退货，默认为0，表示用户在确认收货后15天就不能退货了。", "decoration.common.pageSet": "页面设置", "decoration.common.pageTitle": "页面标题", "decoration.common.pageTitlePlaceholder": "请输入页面标题", "decoration.common.tabEditContent": "内容", "decoration.common.tabEditStyle": "样式", "decoration.common.currentPageTemplate": "当前页面模板", "decoration.common.unnamedPage": "未命名页面", "decoration.common.editPageName": "编辑页面名称", "decoration.common.enterPageName": "请输入页面名称", "decoration.common.saveAndPreview": "保存并预览", "decoration.common.saveAndClose": "保存并关闭", "decoration.common.styleOne": "风格1", "decoration.common.styleTwo": "风格2", "decoration.common.styleThree": "风格3", "decoration.common.styleFour": "风格4", "decoration.common.styleFive": "风格5", "decoration.common.textStyle": "文字样式", "decoration.common.titleContent": "内容设置", "decoration.common.titleText": "标题名称", "decoration.common.textAlign": "对齐方式", "decoration.common.textAlignLeft": "居左", "decoration.common.textAlignCenter": "居中", "decoration.common.titleColor": "标题颜色", "decoration.common.subTitleColor": "副标题颜色", "decoration.common.subTitle": "副标题", "decoration.common.subTitlePlaceholder": "请输入副标题", "decoration.common.more": "右侧文字", "decoration.common.morePlaceholder": "请输入文字", "decoration.common.moreControl": "右侧按钮", "decoration.common.showText": "显示", "decoration.common.hideText": "隐藏", "decoration.common.leftBlock": "左侧色块", "decoration.common.titleFontSize": "标题大小", "decoration.common.subTitleFontSize": "副标题大小", "decoration.common.backgroundSet": "背景设置", "decoration.common.backgroundColor": "背景颜色", "decoration.common.pageSetBackgroundImageTips": "宽度自适应（最大150px），高度28px", "decoration.common.backgroundImageTip": "建议尺寸：宽750px高度不限", "decoration.common.backgroundHeightScale": "背景图高度比例", "decoration.common.backgroundHeightScaleTip": "为0时背景高度自适应展示", "decoration.common.spacingSet": "间距设置", "decoration.common.horizontalSpacing": "左右边距", "decoration.common.horizontalSpacingTip": "此设置将影响所有组件的左右边距", "decoration.common.styleSelect": "风格选择", "decoration.common.navBarStyle": "导航样式", "decoration.common.isShowTitle": "是否展示标题", "decoration.common.myLocation": "我的位置", "decoration.common.styleSearchOne": "搜索", "decoration.common.styleSearchTwo": "logo+搜索", "decoration.common.styleSearchThree": "标题+搜索", "decoration.common.searchContent": "搜索内容", "decoration.common.searchPlaceholder": "请输入关键词", "decoration.common.searchHotWord": "搜索热词", "decoration.common.searchHotWordPlaceholder": "请输入搜索热词", "decoration.common.searchTips": "鼠标拖拽可调整顺序", "decoration.common.logoTitle": "logo图", "decoration.common.logoTips": "建议112×30", "decoration.common.searchTitle": "标题", "decoration.common.searchTitlePlaceholder": "请输入标题内容", "decoration.common.showTime": "显示时间", "decoration.common.time": "秒", "decoration.common.showTimePlaceholder": "请输入显示时间", "decoration.common.searchBox": "搜索框", "decoration.common.searchHotWordText": "热词文字", "decoration.common.solidLine": "实线", "decoration.common.dashedLine": "虚线", "decoration.common.selectLine": "风格", "decoration.common.lineColor": "线条颜色", "decoration.common.lineWidth": "线条宽度", "decoration.common.titleHeight": "高度设置", "decoration.common.blankHeight": "组件高度", "decoration.common.titleContentTips": "建议：图片宽度750，高度200-950，支持jpg、png", "decoration.common.hotPreview": "热区预览", "decoration.common.hotSet": "热区设置", "decoration.common.addHotArea": "添加热区", "decoration.common.editHotArea": "编辑热区", "decoration.common.uploadImgTips": "请先上传图片", "decoration.common.hotManage": "热区管理", "decoration.common.placeholderHotLink": "请选择链接", "decoration.common.toSetHot": "已设置", "decoration.common.toSetHotNum": "个热区", "decoration.common.hotArea": "热区", "decoration.common.hotAreaRule": "未设置链接", "decoration.common.goodsListSet": "列表设置", "decoration.common.oneCols": "单列展示", "decoration.common.towColsVertical": "两列展示(纵向)", "decoration.common.threeColsVertical": "三列展示", "decoration.common.towColsHorizontal": "两列展示(横向)", "decoration.common.bigImage": "大图展示", "decoration.common.toSlide": "左右滑动展示", "decoration.common.goodsSet": "商品设置", "decoration.common.selectType": "选择方式", "decoration.common.isGoods": "指定商品", "decoration.common.isType": "指定分类", "decoration.common.checkGoods": "选择商品", "decoration.common.checkType": "选择分类", "decoration.common.goodsNum": "商品数量", "decoration.common.goodsSort": "商品排序", "decoration.common.sortAll": "综合", "decoration.common.sortVolume": "销量", "decoration.common.sortPrice": "价格", "decoration.common.showContent": "显示内容", "decoration.common.showInfo": "展示信息", "decoration.common.goodsName": "商品标题", "decoration.common.goodsSubName": "商品副标题", "decoration.common.goodsPrice": "商品价格", "decoration.common.originalPrice": "划线价格", "decoration.common.goodsSale": "商品销量", "decoration.common.userPrice": "会员价格", "decoration.common.shoppingCarBtn": "购物车按钮", "decoration.common.isShowCar": "是否显示", "decoration.common.carBtnStyle": "按钮样式", "decoration.common.placeholderCarContent": "请输入内容", "decoration.common.placeholderCarGoodsType": "请选择商品分类", "decoration.common.placeholderCarGoods": "请选择商品", "decoration.common.carBtnEffect": "按钮效果", "decoration.common.carBtnEffect1": "进入商品详情页", "decoration.common.carBtnEffect2": "商品加购", "decoration.common.subscriptSet": "角标设置", "decoration.common.subscriptStyle": "显示样式", "decoration.common.subscriptStyle1": "不显示", "decoration.common.subscriptStyle2": "系统图标", "decoration.common.subscriptStyle3": "自定义", "decoration.common.subBtnText": "角标文字", "decoration.common.subBtnImg": "添加图片", "decoration.common.subBtnImgSize": "建议尺寸76*76", "decoration.common.goodsStyle": "商品样式", "decoration.common.goodsNameStyle": "商品名称", "decoration.common.goodsNameStyle1": "常规", "decoration.common.goodsNameStyle2": "加粗", "decoration.common.imgBuildRadiusTop": "上圆角", "decoration.common.imgBuildRadiusBottom": "下圆角", "decoration.common.buyBtn": "购买按钮", "decoration.common.buyBtnColor": "按钮颜色", "decoration.common.carBtnTBuildRadius": "圆角", "decoration.common.tabStyle1": "样式1", "decoration.common.tabStyle2": "样式2", "decoration.common.tabStyle3": "样式3", "decoration.common.tabStyle4": "样式4", "decoration.common.tabSet": "选项卡设置", "decoration.common.checkTabGoods": "已选", "decoration.common.tabSetTips": "点击下方选项卡可进行编辑；鼠标拖拽版块可调整顺序", "decoration.common.goodsShow": "商品展示", "decoration.common.tabTitle": "标题", "decoration.common.tabSubTitle": "副标题", "decoration.common.tabStyle": "选项卡样式", "decoration.common.tabBackground": "背景", "decoration.common.tabTitleUnCheckColor": "标题未选", "decoration.common.tabTitleCheckColor": "标题选中", "decoration.common.isTabLine": "选项条", "decoration.common.isTabSubTitle": "副标题", "decoration.common.isTrue": "开启", "decoration.common.isFalse": "关闭", "decoration.common.boxColor": "框颜色", "decoration.common.goodsInfo": "商品信息", "decoration.common.goodsInfoPrice": "价格", "decoration.common.goodsInfoCollect": "收藏", "decoration.common.goodsInfoShare": "分享", "decoration.common.goodsInfoTitle": "标题", "decoration.common.goodsInfoSubTitle": "副标题", "decoration.common.goodsInfoSubSale": "销量", "decoration.common.goodsInfoVolume": "库存", "decoration.common.goodsInfoUsePrice": "会员价", "decoration.common.pointsGiveaway": "积分赠送", "decoration.common.pointsDeduction": "积分抵扣", "decoration.common.goodsInfoService": "服务保障", "decoration.common.goodsInfoStatus": "显示状态", "decoration.common.isShowNum": "显示评论个数", "decoration.common.goodsDetailSet": "组件边距", "decoration.common.detailTop": "上边距", "decoration.common.detailBottom": "下边距", "decoration.common.detailLeftOrRight": "左右边距", "decoration.common.menuInfo": "菜单信息", "decoration.common.home": "首页", "decoration.common.customer": "客服", "decoration.common.shoppingCar": "购物车", "decoration.common.homeText": "首页文字", "decoration.common.placeholderHomeText": "请输入首页文字", "decoration.common.showBuyBtn": "加购按钮", "decoration.common.selectPreviewGoods": "选择预览商品", "decoration.common.richTextContentSet": "富文本", "decoration.common.noticeStyle": "公告风格", "decoration.common.noticeType": "类型", "decoration.common.noticeTypeTitle": "标题类型", "decoration.common.noticeTypeImg": "图片", "decoration.common.noticeTypeText": "文字", "decoration.common.noticeTypeTextPlaceholder": "请输入公告标题", "decoration.common.noticeTitle": "标题文字", "decoration.common.addNotice": "添加公告", "decoration.common.noticeText": "公告内容", "decoration.common.noticeScrollWay": "滚动方式", "decoration.common.noticeUpDown": "上下滚动", "decoration.common.noticeHorizontal": "横向滚动", "decoration.common.noticeShowType": "点击类型", "decoration.common.noticeShowPopUp": "弹出公告内容", "decoration.common.noticeShowLink": "跳转链接", "decoration.common.textSet": "文字设置", "decoration.common.textFontSize": "文字大小", "decoration.common.textFontWeight": "文字粗细", "decoration.common.fontWeightNormal": "常规", "decoration.common.fontWeightBold": "加粗", "decoration.common.buttonSet": "按钮设置", "decoration.common.buttonPosition": "右侧按钮", "decoration.common.buttonShow": "显示", "decoration.common.buttonHide": "隐藏", "decoration.common.show": "显示", "decoration.common.hide": "隐藏", "decoration.common.linkName": "链接", "decoration.common.uploadImage": "上传图片", "decoration.common.uploadImageTips": "建议尺寸: 28px*28px", "decoration.common.carouselImage": "内容设置", "decoration.common.carouselImageInterval": "轮播时间", "decoration.common.carouselImageIntervalPlaceholder": "请输入轮播时间", "decoration.common.intervalTips": "秒", "decoration.common.carouselImageSet": "轮播图设置", "decoration.common.carouselImageRadiusTopLeft": "上圆角", "decoration.common.carouselImageRadiusBottomRight": "下圆角", "decoration.common.carouselImageIndicator": "指示器设置", "decoration.common.carouselImageIndicatorStyle": "指示器样式", "decoration.common.carouselImageIndicatorPosition": "指示器位置", "decoration.common.carouselImageIndicatorSelectedColor": "选中的样式", "decoration.common.carouselImageConventionColor": "常规样式", "decoration.common.ceramicShow": "展示设置", "decoration.common.ceramicNavMode": "导航样式", "decoration.common.ceramicShowStyle": "展示样式", "decoration.common.ceramicContent": "内容设置", "decoration.common.ceramicTips": "建议尺寸90 * 90px", "decoration.common.ceramicButton": "按钮", "decoration.common.ceramicImage": "图片设置", "decoration.common.ceramicImageShape": "图片形状", "decoration.common.indicatorColor": "指示器颜色", "decoration.common.indicatorStyle": "指示器样式", "decoration.common.graphicNavModeTitle": "展示设置", "decoration.common.graphicNavSelectMode": "导航样式", "decoration.common.graphicNavShowStyle": "展示风格", "decoration.common.graphicNavStyleFixed": "固定显示", "decoration.common.graphicNavStylePageSlide": "分页滑动", "decoration.common.graphicNavColumnCount": "单行展示", "decoration.common.addGraphicNav": "添加导航", "decoration.common.graphicNavImageShape": "图片形状", "decoration.common.graphicNavImageShapeCircle": "圆形", "decoration.common.graphicNavImageShapeSquare": "方形", "decoration.common.graphicNavImageShapeRound": "圆角", "decoration.common.graphicNavRowCount": "显示行数", "decoration.common.oneLine": "1行", "decoration.common.twoLine": "2行", "decoration.common.threeLine": "3行", "decoration.common.fourLine": "4行", "decoration.common.graphicNavTitlePlaceholder": "请输入导航标题", "decoration.common.changeTemplate": "修改风格", "decoration.common.rubikCubeLayout": "魔方布局", "decoration.common.weChatTitle": "关注引导", "decoration.common.weChatLink": "跳转设置", "decoration.common.weChatLinkUrl": "跳转链接", "decoration.common.weChatLinkImgUrl": "图片跳转", "decoration.common.weChatImage": "图片跳转", "decoration.common.weChatTipsPlaceholder": "请添加公众号二维码作为关注引导", "decoration.common.weChatSearchContent": "搜索内容", "decoration.common.weChatSearchContentPlaceholder": "关注公众号，可第一时间收到最新活动和订单通知等消息推送", "decoration.common.memberContent": "会员信息", "decoration.common.memberInfo": "个人信息", "decoration.common.memberAsset": "资产信息", "decoration.common.memberMarketing": "营销推广", "decoration.common.memberRowCount": "显示样式", "decoration.common.cardBgSetting": "卡片背景", "decoration.common.cardBgSettingColor": "背景色", "decoration.common.cardBgSettingImg": "背景图片", "decoration.common.cardStyle": "卡片样式", "decoration.common.decorationStyle": "装饰样式", "decoration.common.cardPattern": "卡片花纹", "decoration.common.cardGradient": "背景色渐变", "decoration.common.assetColor": "资产颜色", "decoration.common.videoContent": "内容设置", "decoration.common.videoType": "视频类型", "decoration.common.uploadVideo": "上传视频", "decoration.common.videoUrl": "视频链接", "decoration.common.posterUrl": "视频封面", "decoration.common.videoRatio": "视频比例", "decoration.common.thirdUrlPlaceholder": "请输入第三方视频链接", "decoration.common.piece": "个", "decoration.common.image": "图片上传", "decoration.common.title": "标题", "decoration.common.titlePlaceholder": "请输入标题", "decoration.common.buttonText": "按钮文字", "decoration.common.buttonTextPlaceholder": "请输入按钮文字", "decoration.common.modeGraphic": "图文", "decoration.common.modeImg": "图片", "decoration.common.modeText": "文字", "decoration.common.checked": "选中", "decoration.common.unchecked": "未选中", "decoration.common.singleLine": "一行显示", "decoration.common.multiLine": "多行显示", "decoration.common.titleContentSet": "内容设置", "decoration.common.iconGroupLinkTips": "建议上传尺寸相同的图片，推荐尺寸48*48", "decoration.common.titleSearchText": "提示文字", "decoration.common.hintText": "提示文字", "decoration.common.hintTextPlaceholder": "请输入提示文字", "decoration.common.decoration": "装修", "decoration.common.saveImage": "保存图片", "decoration.common.displaySettings": "展示设置", "decoration.common.selectStyle": "选择风格", "decoration.common.commonStyleTitle": "组件样式", "decoration.common.textColor": "文字颜色", "decoration.common.textActiveColor": "选中文字颜色", "decoration.common.containerGradient": "底部背景", "decoration.common.backgroundGradient": "组件背景", "decoration.common.backgroundImage": "背景图片", "decoration.common.backgroundImagePlaceholder": "请输入图片URL", "decoration.common.opacity": "透明度", "decoration.common.itemStyle": "子项样式", "decoration.common.imageSet": "图片设置", "decoration.common.imageGap": "图片间距", "decoration.common.addText": "添加", "decoration.common.codePreviewTip": "扫描右侧二维码查看，点击跳转", "decoration.common.titleStyleSet": "标题设置", "decoration.common.editTip": "正在编辑", "decoration.common.containerTopPadding": "上边距", "decoration.common.containerBottomPadding": "下边距", "decoration.common.containerLeftPadding": "左边距", "decoration.common.containerRightPadding": "右边距", "decoration.common.containerVerticalPadding": "上下边距", "decoration.common.containerHorizontalPadding": "左右边距", "decoration.common.containerTopMargin": "上外边距", "decoration.common.containerBottomMargin": "下外边距", "decoration.common.containerLeftMargin": "左外边距", "decoration.common.containerRightMargin": "右外边距", "decoration.common.containerVerticalMargin": "上下外边距", "decoration.common.containerHorizontalMargin": "左右外边距", "decoration.common.borderRadius": "圆角设置", "decoration.common.topRadius": "上圆角", "decoration.common.bottomRadius": "下圆角", "decoration.common.topLeft": "左上", "decoration.common.topRight": "右上", "decoration.common.bottomLeft": "左下", "decoration.common.bottomRight": "右下", "decoration.common.itemBorderRadius": "子项圆角", "decoration.common.itemTopRadius": "子项上圆角", "decoration.common.itemBottomRadius": "子项下圆角", "decoration.common.styleSet": "样式设置", "decoration.common.leavePageTitleTips": "温馨提示", "decoration.common.leavePageContentTips": "确定离开此页面？系统可能不会保存您所做的更改。", "decoration.common.diyPageTitlePlaceholder": "请输入页面名称", "decoration.common.moveUpComponent": "上移", "decoration.common.moveDownComponent": "下移", "decoration.common.copyComponent": "复制", "decoration.common.deleteComponent": "删除", "decoration.common.resetComponent": "重置", "decoration.common.delComponentTips": "确认要删除当前组件吗？", "decoration.common.componentCanOnlyAdd": "组件只能添加", "decoration.common.resetComponentTips": "确认要重置当前组件为默认数据吗？", "decoration.common.showComponent": "显示", "decoration.common.hideComponent": "隐藏", "decoration.common.imageUrlTip": "请上传图片", "decoration.common.link": "链接", "decoration.common.selectLink": "选择链接", "decoration.common.linkPlaceholder": "请输入链接地址", "decoration.common.navIcon": "导航图标", "decoration.common.navName": "导航名称", "decoration.common.navLink": "导航链接", "decoration.common.navNamePlaceholder": "请输入导航名称", "decoration.common.isLabel": "是否启用标签", "decoration.common.labelText": "标签内容", "decoration.common.labelTextPlaceholder": "请输入标签内容", "decoration.common.labelBgColor": "标签背景", "decoration.common.labelTextColor": "标签颜色", "decoration.common.mallText": "商城", "decoration.common.goodsText": "商品", "decoration.common.pluginText": "插件", "decoration.common.otherText": "其他", "decoration.common.developTitle": "开发环境配置", "decoration.common.wapDomain": "wap域名（WAP_DOMAIN）", "decoration.common.wapDomainPlaceholder": "请输入wap域名", "decoration.common.pagePath": "页面路径（PAGE_PATH）", "decoration.common.pagePathPlaceholder": "请输入页面路径", "decoration.common.settingTips": "点击查看如何配置", "decoration.common.pageLoading": "页面加载中，无法添加组件", "decoration.common.componentNotExist": "组件不存在，无法更新属性", "decoration.common.componentMaxLimit": "组件最多只能添加{count}个", "decoration.common.componentNotCopy": "该组件不能复制", "decoration.common.componentNotAdd": "轮播搜索不能和搜索组件与选项卡组件同时存在", "decoration.common.componentNotMoved": "固定位置组件不能移动", "decoration.common.componentNotVisible": "组件不可见", "decoration.common.pageTitleEmpty": "页面标题不能为空", "decoration.common.componentVerifyFailed": "组件验证失败", "decoration.common.updateComponentFailed": "更新组件属性失败: {error}", "decoration.common.backgroundSize": "背景尺寸", "decoration.common.sizeCover": "填充（覆盖）", "decoration.common.sizeContain": "适应（包含）", "decoration.common.sizeCustom": "自定义", "decoration.index.newPage": "新建页面", "decoration.index.editPage": "编辑页面", "decoration.index.pageId": "页面ID", "decoration.index.pageName": "页面名称", "decoration.index.pageType": "页面类型", "decoration.index.createTime": "创建时间", "decoration.index.pageTitle": "装修", "decoration.index.showNav": "显示导航", "decoration.index.setHome": "设为首页", "decoration.link.linkPlaceholder": "请选择跳转链接", "decoration.link.diyLinkType": "链接类型", "decoration.link.diyLinkUrl": "链接地址", "decoration.link.diyLinkUrlPlaceholder": "请输入链接地址", "decoration.link.diyAppletId": "小程序AppID", "decoration.link.diyAppletIdPlaceholder": "请输入小程序AppID", "decoration.link.diyAppletPage": "小程序路径", "decoration.link.diyAppletPagePlaceholder": "请输入小程序路径", "decoration.link.diyMakePhone": "拨打电话", "decoration.link.diyMakePhonePlaceholder": "请输入拨打电话", "decoration.share.h5ShareTitle": "H5商品分享标题", "decoration.share.miniProgramShareTitle": "小程序分享标题", "decoration.share.h5ShareTitleValue": "自定义前缀营销文案", "decoration.share.miniProgramShareTitleValue": "自定义前缀营销文案", "decoration.share.onlyShowGoodsName": "仅展示商品名称", "decoration.share.showCustomPrefixMarketingCopy": "展示自定义前缀营销文案", "decoration.share.goodsName": "和商品名称", "decoration.share.example": "例：自定义文案为  有人拍了拍你，推荐了 展示信息为：有人拍了拍你，推荐了", "decoration.share.example1": "12.99元 小米手机手机壳，定制手机壳，设计感手机壳", "decoration.share.show": "展示", "decoration.share.hide": "不展示", "decoration.share.posterStyle": "商品海报样式", "decoration.share.posterShareUser": "海报分享人", "decoration.share.posterStoreInfo": "店铺信息", "decoration.share.poster": "海报", "decoration.share.weChat": "微信对话", "decoration.share.bindStoreLogo": "绑定店铺logo", "decoration.share.bindStoreInfo": "绑定店铺信息", "decoration.share.bindStoreLogoTips": "展示在二维码中", "decoration.share.bindStoreInfoTips": "展示在海报中", "decoration.share.inviteText": "邀请好友，解锁专属好礼", "decoration.share.editText": "编辑文字", "decoration.share.posterBackground": "海报背景", "decoration.share.posterBackgroundTips": "建议尺寸：590*1050 尺寸不匹配时图片将会被压缩或拉伸", "decoration.tabbar.tabbar": "底部导航", "decoration.tabbar.tabbarGroupName": "导航组名称", "decoration.tabbar.tabbarGroupKey": "导航组标识", "decoration.tabbar.tabbarGroupType": "导航组类型", "decoration.tabbar.tabbarGroupStatus": "状态", "decoration.tabbar.dragTip": "拖动可调整位置", "decoration.tabbar.tabbarIcon": "导航图标", "decoration.tabbar.tabbarTitle": "导航标题", "decoration.tabbar.tabbarLink": "导航链接", "decoration.tabbar.tabbarVisible": "显示导航", "decoration.tabbar.iconPathTip": "请上传导航未选中图标", "decoration.tabbar.selectedIconPathTip": "请上传导航选中图标", "decoration.tabbar.tabbarGroupNameTip": "请输入导航组名称", "decoration.tabbar.addTabbarGroupTip": "设置至少添加2个导航，最多添加5个导航", "decoration.theme.themeStyle": "主题风格", "decoration.theme.themeColor": "主题配色", "decoration.theme.previewEffect": "预览效果", "decoration.theme.themeRed": "热情红", "decoration.theme.themeBlue": "天空蓝", "decoration.theme.themePurple": "魅力紫", "decoration.theme.themeGreen": "生鲜绿", "decoration.theme.themePink": "活力粉", "downloadCenter.common.fileName": "文件名称", "downloadCenter.common.applyTime": "发起申请时间", "downloadCenter.common.fileGenerationTime": "文件生成时间", "downloadCenter.common.currentStatus": "当前状态", "downloadCenter.common.download": "下载", "goods.category.pageTitle": "商品分类", "goods.category.addButton": "新增分类", "goods.category.editButton": "编辑分类", "goods.category.deleteButton": "删除分类", "goods.category.deleteConfirm": "是否删除该分类？", "goods.category.categoryName": "分类名称", "goods.category.categoryNamePlaceholder": "请输入分类名称", "goods.category.level": "分类等级", "goods.category.categoryImage": "分类图片", "goods.category.categoryImagePlaceholder": "请上传分类图片", "goods.category.parentCategory": "上级分类", "goods.category.parentCategoryPlaceholder": "请选择上级分类", "goods.category.levelOne": "一级分类", "goods.category.levelTwo": "二级分类", "goods.category.levelThree": "三级分类", "goods.category.imageHelpText": "建议尺寸200*200像素", "goods.category.sort": "排序", "goods.category.sortPlaceholder": "请输入排序值", "goods.category.status": "状态", "goods.category.statusEnable": "启用", "goods.category.statusDisable": "禁用", "goods.category.id": "分类ID", "goods.category.name": "分类名称", "goods.category.image": "分类图片", "goods.category.parent": "上级分类", "goods.category.expandCollapseButton": "全部展开/折叠", "goods.goods.pageTitle": "商品管理", "goods.goods.goodsId": "商品ID", "goods.goods.placeholderGoodsId": "请输入商品ID", "goods.goods.coverPic": "图标", "goods.goods.placeholderCoverPic": "请上传图片", "goods.goods.goodsName": "商品名称", "goods.goods.placeHolderGoodsName": "请输入保障商品名称", "goods.goods.goodsInfo": "商品信息", "goods.goods.searchGoodsName": "请输入商品名称/商品ID", "goods.goods.goodsForIn": "商品来源", "goods.goods.goodsForIn1": "自营门店", "goods.goods.placeHolderGoodsForIn": "请选择商品来源", "goods.goods.goodsType": "商品类型", "goods.goods.placeGoodsType": "请选择商品类型", "goods.goods.goodsTypeTips": "商品属性，当商品保存后，无法修改，请谨慎选择", "goods.goods.goodsType1": "实体商品", "goods.goods.goodsType2": "虚拟商品", "goods.goods.subtitle": "商品副标题", "goods.goods.placeSubtitle": "请输入商品副标题", "goods.goods.unit": "商品单位", "goods.goods.placeUnit": "请输入商品单位", "goods.goods.unitTips": "填写后，在商品详情页，在价格后面对应单位，例如：智利车厘子/箱", "goods.goods.bannerImages": "商品轮播图", "goods.goods.isVideo": "添加视频", "goods.goods.videoType": "讲解视频", "goods.goods.videoType1": "上传视频", "goods.goods.videoType2": "视频链接", "goods.goods.video_url": "视频原地址", "goods.goods.video_cover_url": "视频封面", "goods.goods.serviceIds": "保障服务", "goods.goods.placeServiceIds": "请选择保障服务", "goods.goods.availableToFranchise": "是否开放加盟门店", "goods.goods.availableToFranchise1": "是", "goods.goods.availableToFranchise2": "否", "goods.goods.isOnSaleInfo": "商品状态", "goods.goods.isOnSaleInfo1": "上架", "goods.goods.isOnSaleInfo2": "下架", "goods.goods.specificationsImage": "规格图", "goods.goods.originalPrice": "划线价", "goods.goods.originalPriceTips": "商品没有优惠的情况下，划线价在商品详情页展示", "goods.goods.placeCostPrice": "请输入成本价", "goods.goods.costPriceTips": "成本价将不会对用户展示，便于商家统计使用", "goods.goods.placeStock": "请输入库存数量", "goods.goods.weight": "商品重量", "goods.goods.placeWeight": "请输入商品重量", "goods.goods.volume": "商品体积", "goods.goods.placeVolume": "请输入商品体积", "goods.goods.stockWarning": "库存预警", "goods.goods.placeStockWarning": "请输入预警数量", "goods.goods.stockWarningTips": "商品库存小于预警数量时，则商品列表库存数量标红显示", "goods.goods.freightType": "物流方式", "goods.goods.freightType1": "快递物流", "goods.goods.freightRulesType": "运费设置", "goods.goods.freightRulesType1": "统一运费", "goods.goods.freightRulesType2": "运费模板", "goods.goods.placeFreightRulesType1": "请输入统一运费", "goods.goods.placeFreightRulesType2": "请选择运费模板", "goods.goods.virtualSales": "虚拟销量", "goods.goods.placeVirtualSales": "请输入数量", "goods.goods.virtualSalesTips": "当编辑虚拟销量后，商品详情页面展示的销量为真实销量+虚拟销量", "goods.goods.goodsPrice": "商品售价", "goods.goods.placeHolderGoodsPrice": "请输入商品售价", "goods.goods.goodsSales": "销量", "goods.goods.placeHolderGoodsSales": "请输入销量", "goods.goods.goodsStock": "库存", "goods.goods.placeHolderGoodsStock": "请输入商品库存", "goods.goods.goodsSpe": "商品规格", "goods.goods.placeGoodsSpe": "请选择规格模板", "goods.goods.addGoodsSpe": "添加规格模板", "goods.goods.placeGoodsValue": "请输入规格值", "goods.goods.addGoodsValue": "添加", "goods.goods.toGenerate": "立即生成", "goods.goods.goodsInfoTable": "商品属性", "goods.goods.toAllSet": "批量设置", "goods.goods.toRemove": "清空", "goods.goods.sort": "排序", "goods.goods.placeHolderSort": "请输入排序", "goods.goods.isOnSale": "上架/下架", "goods.goods.cateRelate": "商品分类", "goods.goods.placeCateRelate": "请选择商品分类", "goods.goods.tagRelate": "商品标签", "goods.goods.placeTagRelate": "请选择商品标签", "goods.goods.costPrice": "成本价", "goods.goods.createdAt": "创建时间", "goods.goods.deleteTips": "删除商品请谨慎操作，是否继续？", "goods.goods.addGoodsBtn": "新增商品", "goods.goods.importBtn": "批量导入", "goods.goods.tabType1": "全部", "goods.goods.tabType2": "销售中", "goods.goods.tabType3": "已售罄", "goods.goods.tabType4": "仓库中", "goods.goods.tabType5": "库存预警", "goods.goods.updateSuccess": "更新成功", "goods.goods.updateError": "更新失败", "goods.goods.placeInfo": "请填写完整信息", "goods.goods.saveError": "操作失败", "goods.goods.nextBtn": "下一步", "goods.goods.prevBtn": "上一把", "goods.goods.saveBtn": "保存", "goods.service.pageTitle": "保障服务", "goods.service.serviceId": "保障服务ID", "goods.service.placeholderServiceId": "请输入保障服务ID", "goods.service.customIconUrl": "图标", "goods.service.customIconUrlType": "图标类型", "goods.service.customIconUrlType1": "默认图标", "goods.service.customIconUrlType2": "自定义图标", "goods.service.placeholderCustomIconUrl": "请上传图片", "goods.service.serviceName": "保障服务", "goods.service.placeHolderServiceName": "请输入保障服务名称", "goods.service.searchServiceName": "请输入昵称ID/名称", "goods.service.description": "内容描述", "goods.service.placeHolderDescription": "请输入内容描述", "goods.service.sort": "排序", "goods.service.placeHolderSort": "请输入排序", "goods.service.status": "显示状态", "goods.service.status0": "隐藏", "goods.service.status1": "显示", "goods.service.placeStatus": "请输入显示状态", "goods.service.createdAt": "创建时间", "goods.service.deleteTips": "删除该保障服务，是否继续？", "goods.service.addServiceBtn": "新建保障服务", "goods.service.addSpeBtn": "新增规格模板", "goods.service.deleteSpeTips": "删除该规格模板，是否继续？", "goods.service.speTempID": "模板ID", "goods.service.cateName": "规格模板", "goods.service.addCateBtn": "新增规格", "goods.service.addBtn": "添加", "goods.service.placeHolderCateName": "请输入模板名称", "goods.service.placeCateName": "请输入模板名称/ID", "goods.service.groupName": "规格名", "goods.service.groupNameError": "规格名称不能为空", "goods.service.placeGroupName": "请输入规格名称", "goods.service.groupValue": "规格值", "goods.service.placeGroupValue": "请输入规格值", "goods.service.editCateTitle": "编辑规格模板", "goods.service.addCateTitle": "新增规格模板", "goods.service.placeInfo": "请填写完整信息", "goods.service.saveError": "操作失败", "goods.service.saveBtn": "保存", "goods.tags.addTitle": "新增标签", "goods.tags.editTitle": "编辑标签", "goods.tags.id": "标签ID", "goods.tags.name": "商品标签", "goods.tags.sort": "排序", "goods.tags.status": "显示状态", "goods.tags.newTag": "新建标签", "goods.tags.tagName": "标签名称", "goods.tags.tagNamePlaceholder": "请输入标签名称", "goods.tags.fontColor": "字体颜色", "goods.tags.fontColorPlaceholder": "请选择字体颜色", "goods.tags.bgColor": "背景颜色", "goods.tags.bgColorPlaceholder": "请选择背景颜色", "goods.tags.sortPlaceholder": "请输入排序值", "goods.tags.statusShow": "显示", "goods.tags.statusHide": "隐藏", "goods.tags.fontColorTip": "若未设置颜色，则默认白色", "goods.tags.bgColorTip": "若未设置颜色，则默认红色", "home.common.batchDelete": "批量删除", "home.common.batchMarkAsRead": "批量标记为已读", "home.common.view": "查看", "home.common.delete": "删除", "home.common.notificationDetail": "通知详情", "intelligentApplications.common.feiyuApp": "飞鱼应用", "intelligentApplications.common.aiAgentApp": "AI智能体", "intelligentApplications.common.intelligentApplication": "智能应用", "intelligentApplications.common.appSettings": "应用配置", "intelligentApplications.common.feiyuAppSettings": "飞鱼应用配置", "intelligentApplications.common.basicSettings": "基本设置", "intelligentApplications.common.requestLog": "请求日志", "intelligentApplications.common.isOpenPlugin": "是否开启插件", "intelligentApplications.common.openPlugin": "开启", "intelligentApplications.common.closePlugin": "关闭", "intelligentApplications.common.callbackAddress": "回调地址", "intelligentApplications.common.specifyReturnLeadPool": "指定回传线索池", "kong.addTitle": "新增", "kong.editTitle": "编辑", "kong.id": "ID", "kong.name": "名称", "kong.status": "状态", "kong.type": "类型", "kong.createDate": "创建日期", "kong.description": "描述", "kong.typeOne": "类型一", "kong.typeTwo": "类型二", "kong.typeThree": "类型三", "kong.typeFour": "类型四", "kong.typeUnknown": "未知", "kong.typePlaceholder": "请选择类型", "kong.all": "全部", "kong.enable": "启用", "kong.disable": "禁用", "kong.nameLabel": "名称", "kong.namePlaceholder": "请输入名称", "kong.nameTooltip": "这是一个提示", "kong.statusLabel": "状态", "kong.statusPlaceholder": "请选择状态", "kong.descriptionLabel": "描述", "kong.descriptionPlaceholder": "请输入描述", "kong.dialogAdd": "弹窗新增", "leads.common.addLeads": "新增线索", "leads.common.editLeads": "编辑线索", "leads.common.importLeads": "导入线索", "leads.common.exportLeads": "导出线索", "leads.common.moveLeads": "转移线索", "leads.common.receiveLeads": "领取", "leads.common.leadsDetail": "详情", "leads.common.distributeLeads": "分配", "leads.common.deleteLeads": "删除", "leads.common.deleteLeadReason": "删除原因", "leads.common.callStatusFollow": "通话状态跟进", "leads.common.backToSea": "退回公海", "leads.common.transferClient": "转客户", "leads.common.setInvalid": "设为无效", "leads.common.deleteLeadReasonRequired": "请输入删除原因", "leads.common.customer": "联系人", "leads.common.customerName": "联系人姓名", "leads.common.customerNamePlaceholder": "请输入联系人名称", "leads.common.customerNameRequired": "请输入不超过30个字的联系人姓名", "leads.common.customerPhone": "联系电话", "leads.common.customerPhonePlaceholder": "请输入联系电话", "leads.common.customerPhoneRequired": "已有相关线索，请勿重复录入！", "leads.common.customerPhoneExist": "手机号已存在", "leads.common.customerWechat": "联系人微信", "leads.common.customerWechatPlaceholder": "请输入线索联系人微信", "leads.common.customerWechatRequired": "微信号不能超过30位", "leads.common.customerSex": "联系人性别", "leads.common.customerAge": "联系人年龄", "leads.common.customerAgePlaceholder": "请输入联系人年龄", "leads.common.customerAgeRequired": "年龄只能是两位以内的纯数字！", "leads.common.customerSexNone": "未知", "leads.common.customerSexMale": "男", "leads.common.customerSexFemale": "女", "leads.common.rgsLocation": "电话归属地", "leads.common.companyName": "企业名称", "leads.common.companyNamePlaceholder": "请输入公司名称", "leads.common.companyNameRequired": "请输入不超过30个字的企业名称！", "leads.common.industry": "所属行业", "leads.common.sex": "性别", "leads.common.age": "年龄", "leads.common.wechat": "微信", "leads.common.callStatus": "通话状态", "leads.common.sopStep": "SOP阶段", "leads.common.leadLabel": "线索标签", "leads.common.entryMethod": "录入方式", "leads.common.source": "渠道", "leads.common.demandSource": "需求来源", "leads.common.sourceOrin": "渠道来源", "leads.common.sourceOrinRequired": "请选择渠道来源", "leads.common.sourceAuth": "渠道账号", "leads.common.sourceAuthPlaceholder": "请输入渠道账号", "leads.common.followStatus": "跟进状态", "leads.common.otherInfo": "其他信息", "leads.common.chooseLeadPool": "公海池", "leads.common.selectPlaceholder": "不限", "leads.common.selectPlaceholderForm": "请选择", "leads.common.addTime": "添加时间", "leads.common.location": "所在地", "leads.common.locationDetail": "详细地址", "leads.common.address": "地址", "leads.common.addressPlaceholder": "请输入详细地址", "leads.common.addressRequired": "详细地址不能超过50个字！", "leads.common.remark": "备注", "leads.common.remarkPlaceholder": "请输入备注", "leads.common.leadDetail": "线索详情", "leads.common.leadOverview": "线索概况", "leads.common.basicInformation": "基本资料", "leads.common.leadInformation": "线索信息", "leads.common.leadStatus": "跟进状态", "leads.common.exportInfo": "请选择导出信息", "leads.common.exportInfoPlaceholder": "导出信息", "leads.common.exportInfoTips": "导出任务已生成，请稍后前往", "leads.common.downloadCenter": "下载中心", "leads.common.leadTags": "线索标签", "leads.common.leadTagsPlaceholder": "请输入线索标签", "leads.common.leadTagsRequired": "线索标签不能超过30个字！", "leads.common.createTime": "创建时间", "leads.common.department": "所属部门", "leads.common.assignTime": "分配时间", "leads.common.firstAssignTime": "首次分配时间", "leads.common.owner": "负责人", "leads.common.collaborator": "协作员", "leads.common.assignedBy": "分配人", "leads.common.lastOperationTime": "最新操作时间", "leads.common.currentPage": "当前位置", "leads.common.creator": "创建人", "leads.common.manageTags": "管理标签", "leads.common.tagCategory": "标签分类", "leads.common.selectedTags": "已选标签", "leads.common.clear": "清空", "leads.common.tagName": "标签名称", "leads.common.tagColor": "标签颜色", "leads.common.tagId": "标签ID", "leads.common.tag": "个", "leads.common.confirmChangeStatus": "确定更改当前状态为", "leads.common.confirmDeleteLead": "您确定删除当前这", "leads.common.confirmDeleteLeadCount": "个线索，删除后当前线索将流入回收站中！", "leads.common.confirmBackToSeaTitle": "退回公海", "leads.common.confirmBackToSea": "退回公海后后当前线索将返回公海池中，请谨慎操作！", "leads.common.confirmBackToSeaReason": "请输入退回公海原因", "leads.common.confirmChangeInvalid": "您确定把当前线索设为", "leads.common.invalid": "无效", "leads.common.confirm": "您确定", "leads.common.confirmChangeInvalidTips": "当前线索的无效标记吗？", "leads.common.executeTask": "执行任务", "leads.common.executeTaskPlaceholder": "请输入执行任务", "leads.common.executeTaskRequired": "请输入执行任务", "leads.common.executeTaskTips": "请输入执行任务", "leads.common.taskTitle": "任务标题", "leads.common.taskContent": "任务描述", "leads.common.taskSummary": "任务总结", "leads.common.taskDetail": "任务详情", "leads.common.taskType": "任务类型", "leads.common.taskSummaryPlaceholder": "请输入任务总结", "leads.common.leadDepartment": "线索归属部门", "link.id": "ID", "link.title": "名称", "link.titlePlaceholder": "请输入名称", "link.titleRequired": "请输入名称", "link.key": "唯一标识", "link.keyPlaceholder": "请输入唯一标识", "link.keyRequired": "请输入唯一标识", "link.url": "跳转链接", "link.urlPlaceholder": "请输入跳转链接", "link.urlRequired": "请输入跳转链接", "link.type": "类型", "link.typePlaceholder": "请选择类型", "link.typeRequired": "请选择类型", "link.typeSystem": "商城系统", "link.typePlugin": "应用插件", "link.typeUnknown": "未知类型", "link.status": "状态", "link.statusRequired": "请选择状态", "link.enable": "启用", "link.disable": "禁用", "link.all": "全部", "link.category": "链接类型", "link.categoryPlaceholder": "请选择链接类型", "link.categoryRequired": "请选择链接类型", "link.addonsName": "应用名称", "link.addonsNamePlaceholder": "请输入应用名称", "link.addonsNameRequired": "请输入应用名称", "link.addTitle": "添加链接", "link.editTitle": "编辑链接", "link.isShare": "是否可分享", "link.isShareRequired": "请选择是否可分享", "link.allow": "允许", "link.notAllow": "不允许", "linkCategory.id": "ID", "linkCategory.title": "名称", "linkCategory.titlePlaceholder": "请输入名称", "linkCategory.titleRequired": "请输入名称", "linkCategory.name": "唯一标识", "linkCategory.namePlaceholder": "请输入唯一标识", "linkCategory.nameRequired": "请输入唯一标识", "linkCategory.createTime": "创建时间", "linkCategory.updateTime": "更新时间", "linkCategory.addTitle": "添加分类", "linkCategory.editTitle": "编辑分类", "meal.list.serial": "序号", "meal.list.name": "套餐名称", "meal.list.explain": "套餐说明", "meal.list.siteNum": "站点数量", "meal.list.createTime": "创建时间", "meal.list.namePlaceholder": "请输入套餐名称", "meal.list.allAppPlaceholder": "请选择应用", "meal.list.confirmDelete": "确定删除{name}？", "meal.list.isPeriod": "周期性有效期", "meal.list.description": "套餐说明", "meal.list.periodName": "永久有效", "meal.list.allApp": "所有应用", "meal.list.createMeal": "创建套餐", "meal.list.editMeal": "编辑套餐", "meal.list.tipsMeal": "温馨提示：套餐新增或删除时，将对已使用该套餐的所有店铺产生影响，请选择客户访问店铺时间较少的时候调整。如：店铺已购买小票打印应用且应用的到期时间小于套餐到期时间，此时修改套餐将小票打印调整为免费时，店铺购买的应用到期时间将保留，且标记“套餐内免费使用”，套餐到期之前可免费使用该应用。如果再次修改套餐取消免费使用，则保留应用到期时间，应用过期后需续费使用。", "meal.list.dayMeal": "天", "meal.list.monthMeal": "月", "meal.list.yearMeal": "年", "notice.send.pageTitle": "消息通知", "notice.send.noticeType": "消息编号", "notice.send.noticeTypeName": "消息类型", "notice.send.user": "接收人", "notice.send.nickname": "昵称", "notice.send.mobile": "手机号", "notice.send.createdAt": "发送时间", "notice.send.noticeTypeName1": "会员", "notice.send.noticeTypeName2": "商家", "notice.send.placeNickname": "请输入昵称/账号/ID", "notice.send.placeType": "请选择消息类型", "notice.send.subNoticeType": "编号", "notice.send.placeSubNoticeType": "请输入编号", "notice.send.subNoticeTypeName": "消息类型", "notice.send.placeSubNoticeTypeName": "请输入消息类型", "notice.send.tempLateId": "模板ID", "notice.send.placeTempLateId": "请输入模板ID", "notice.send.tempLateSign": "模板签名", "notice.send.placeTempLateSign": "请输入模板签名", "notice.send.subSmsDisabled": "短信", "notice.send.subWechatDisabled": "公众号", "notice.send.idDisabled": "是否开启", "notice.send.describeTitle": "模板消息通知必须操作", "notice.send.describeTips1": "1、进入微信公众号后台，打开菜单栏:设置与开发-公众号设置-账号详情，找到“服务类目”栏，点击“详情”按钮，", "notice.send.describeTips2": "2、再点击“添加类目”按钮(点击后需要管理员进行扫码)，添加以下三个类目:", "notice.send.describeTips3": "商家自营-美妆", "notice.send.describeTips4": "商家自营-机械电子产品", "notice.send.describeTips5": "商业服务-软件/建站/技术开发", "notice.send.describeTips6": "注意事项:若模板ID不填写则不会触发对应的模板消息通知!", "notice.send.isExplainTrue": "收起说明", "notice.send.isExplainFalse": "展开说明", "notice.send.tabType1": "会员通知", "notice.send.statusEnable": "开启", "notice.send.statusDisable": "关闭", "order.afterSales.storeName": "门店名称", "order.afterSales.enterStoreName": "请输入门店名称", "order.afterSales.afterSalesNum": "售后编号", "order.afterSales.enterAfterSalesNum": "请输入售后编号", "order.afterSales.orderNum": "订单编号", "order.afterSales.enterOrderNum": "请输入订单编号", "order.afterSales.afterSalesTime": "售后时间", "order.afterSales.afterSalesType": "售后类型", "order.afterSales.selectAfterSalesType": "请选择售后类型", "order.afterSales.afterSalesStatus": "售后状态", "order.afterSales.selectAfterSalesStatus": "请选择售后状态", "order.afterSales.orderType": "订单类型", "order.afterSales.confirmAgreeAfterSales": "确定同意售后?", "order.afterSales.agreeAfterSalesSuccess": "同意售后成功", "order.afterSales.rejectAfterSalesSuccess": "拒绝售后成功", "order.afterSales.courierComp": "快递公司", "order.afterSales.selectCourierComp": "请选择快递公司", "order.afterSales.trackNum": "快递单号", "order.afterSales.enterTrackNum": "请输入快递单号", "order.afterSales.afterSale": "售后", "order.afterSales.afterSaleSuccess": "发货成功", "order.afterSales.afterSaleInfo": "售后信息", "order.afterSales.afterSaleMethod": "售后方式", "order.afterSales.purchaseInfo": "购买信息", "order.afterSales.returnAddress": "退货地址", "order.afterSales.contactPhone": "联系电话", "order.afterSales.contactPerson": "联系人", "order.afterSales.prodInfo": "商品信息", "order.afterSales.negotiationRecord": "协商记录", "order.afterSales.agreeSendReturnAddr": "同意并发送退货地址", "order.afterSales.agreeRefund": "同意退款", "order.afterSales.agreeAfterSales": "同意售后", "order.afterSales.rejectAfterSale": "拒绝售后申请", "order.afterSales.confirmReceiptAndRefund": "确认收货并退款", "order.afterSales.confirmReceiptAndShip": "确定收货并发货", "order.afterSales.rejectReceipt": "拒绝收货", "order.afterSales.confirmReceive": "是否确认收货？", "order.afterSales.confirmReceiveSuccess": "确认收货成功", "order.afterSales.refundAmountExceeded": "退款金额不能大于商品总价", "order.afterSales.rejectionReason": "拒绝理由", "order.afterSales.enterRejectionReason": "请填写拒绝理由", "order.afterSales.enterRefundAmt": "请输入退款金额", "order.afterSales.selectAddr": "选择地址", "order.afterSales.newAddr": "新地址", "order.afterSales.contactInfo": "联系方式", "order.afterSales.contactAddr": "联系地址", "order.afterSales.selectProv": "请选择省", "order.afterSales.selectCity": "请选择市", "order.afterSales.selectDist": "请选择区", "order.afterSales.selectStreet": "请选择街道", "order.afterSales.detailAddr": "详细地址", "order.afterSales.enterDetailAddr": "请输入详细地址", "order.afterSales.enterPrice": "请输入价格", "order.afterSales.enterContactPerson": "请输入联系人", "order.afterSales.enterPhoneNum": "请输入手机号", "order.afterSales.formValidationSuccess": "表单校验成功", "order.afterSales.formValidationFail": "表单校验失败", "order.afterSales.afterSaleProcessing": "售后处理", "order.channel.fileSelect": "选择文件", "order.channel.dataImport": "导入数据", "order.channel.importDone": "导入完成", "order.channel.deliverByOrderNum": "1. 根据订单编号发货，请先，", "order.channel.deliverProductName": "2. 根据订单编号以及商品名称发货，请先", "order.channel.tempDownload": "下载批量发货模板1", "order.channel.suitSingleParcel": "适用于一笔订单一个包裹的情况", "order.channel.suitSingleParcelAll": "适用于一笔订单多个包裹的情况", "order.channel.temp2Download": "下载批量发货模板2", "order.channel.dragOrAddFile": "将文件拖到此处，或点击添加", "order.channel.csvFileOnly": "仅支持格式为.csv的文件", "order.channel.startImporting": "+开始导入", "order.channel.noteMaxRecords": "注：1.一次最多导入3000行记录，超出部分请分批导入", "order.channel.noteKeepPageOpen": "2.导入未完成之前，请勿关闭页面，否则会导致导入失败", "order.channel.importLog": "导入记录", "order.channel.errorCause": "错误原因", "order.channel.memberId": "会员ID", "order.channel.operator": "操作人", "order.channel.importOrderCount": "导入订单数", "order.channel.importSuccessCount": "导入成功数", "order.channel.importFailCount": "导入失败数", "order.channel.status": "状态", "order.channel.importTime": "导入时间", "order.channel.orderNumber": "订单编号", "order.channel.logisticsNumber": "物流单号", "order.channel.reasonForError": "错误原因", "order.channel.operatorPlaceholder": "请输入操作人", "order.channel.importStatus": "导入状态", "order.channel.selectImportStatus": "请选择导入状态", "order.channel.completed": "已完成", "order.channel.processing": "处理中", "order.channel.regTime": "注册时间", "order.channel.checkFailureReason": "查看失败原因", "order.channel.exportFailedOrders": "导出失败订单", "order.channel.importSuccess": "导入成功", "order.channel.startImport": "开始导入中", "order.comment.selectGoods": "选择商品", "order.comment.selectedGoods": "已选", "order.comment.goodsNum": "个", "order.comment.memberAvatar": "会员头像", "order.comment.memberNickname": "会员昵称", "order.comment.evaluationTime": "评价时间", "order.comment.evaluationLevel": "评价等级", "order.comment.evaluationContent": "评价内容", "order.comment.evaluationImage": "评价图片", "order.comment.enterNickname": "请输入会员昵称", "order.comment.isCryptonym": "是否匿名", "order.comment.isCryptonymPlaceholder": "请选择是否匿名", "order.comment.evaluationLevelPlaceholder": "请选择评价等级", "order.comment.selectGoodsPlaceholder": "请选择商品", "order.comment.enterVirtualAvatar": "请上传会员头像", "order.comment.enterVirtualUser": "请输入会员昵称", "order.comment.enterVirtualTime": "请输入评价时间", "order.comment.enterContent": "请输入评价内容", "order.comment.isCryptonym1": "匿名", "order.comment.isCryptonym2": "不匿名", "order.comment.evaluationLevel1": "好评", "order.comment.evaluationLevel2": "中评", "order.comment.evaluationLevel3": "差评", "order.comment.evaluationImageTips": "最多上传6张图片", "order.comment.enterEvaluationContent": "请输入评价内容", "order.comment.enterEvaluationImage": "请上传评价图片", "order.comment.enterEvaluationTime": "请输入评价时间", "order.details.orderNumber": "订单编号", "order.details.transactionNumber": "交易单号", "order.details.orderSource": "订单来源", "order.details.recipientName": "收货人姓名", "order.details.contactInfo": "联系方式", "order.details.deliveryAddress": "收货地址", "order.details.deliveryMethod": "配送方式", "order.details.paidAmount": "实付金额", "order.details.paymentMethod": "支付方式", "order.details.paymentTime": "付款时间", "order.details.buyer": "买家", "order.details.buyerNote": "买家备注", "order.details.orderRemark": "订单备注", "order.details.remark": "备注", "order.details.merchantNote": "商家备注", "order.details.storeNote": "商家备注", "order.details.productTotal": "商品总额", "order.details.totalDiscount": "总优惠金额", "order.details.shippingFee": "运费", "order.details.orderAmount": "订单金额", "order.details.deliveryInfo": "配送信息", "order.details.deliveryMethodDetail": "配送方式", "order.details.recipient": "收货人", "order.details.recipientAddress": "收货地址", "order.details.cancelOrder": "取消订单", "order.details.shipOrder": "发货", "order.details.completeOrder": "订单完成", "order.details.confirmReceipt": "确认收货", "order.details.recipientInfo": "收货人信息", "order.details.paymentInfo": "付款信息", "order.details.buyerMerchantInfo": "备注信息", "order.details.productId": "商品ID", "order.details.unitPrice": "单价(元)", "order.details.purchaseQuantity": "购买数量", "order.details.discountAmount": "优惠金额(元)", "order.details.subTotal": "小计", "order.details.status": "状态", "order.details.configReturnAddrFirst": "请先配置退货地址", "order.details.update": "修改", "order.details.deliverySuccess": "发货成功", "order.details.deliveryFailed": "发货失败", "order.details.remarkSuccess": "备注成功", "order.details.remarkFailed": "备注失败", "order.details.afterSalesType": "售后类型", "order.details.productStatus": "商品状态", "order.details.refundAmtReq": "申请退款金额", "order.details.refundAmt": "退款金额", "order.details.enterRefundAmt": "请输入退款金额", "order.details.enterPrice": "请输入价格", "order.details.refundAmtExceedsReq": "退款金额不能大于申请金额", "order.details.afterSalesProc": "售后处理", "order.details.confirmReceiptAndRefund": "确定收货并退款", "order.details.receiptProcSuccess": "收货处理成功", "order.details.shippingFeeRule": "运费结算规则", "order.details.orderUnshippedRefundRule": "订单中的所有商品在未发货状态下，运费会随着订单的最后一件商品退款退回，若订单中有商品发货，则运费不退回", "order.details.product": "商品", "order.details.refundableAmt": "可退金额(元)", "order.details.refundAmtUnit": "退款金额(元)", "order.details.storeRefund": "商家主动退款", "order.details.enterValidPrice": "请输入正确的价格", "order.details.salePeriod": "未售后", "order.details.buyerOrder": "买家下单", "order.details.buyerPayment": "买家付款", "order.details.merchantShipment": "商家发货", "order.details.buyerReceipt": "买家收货", "order.details.transactionCompletion": "交易完成", "order.details.cancelStepOrder": "已取消", "order.details.userTimeoutNotPayment": "用户超时未支付", "order.details.shipped": "已发货", "order.details.inTransit": "运输中", "order.details.delivered": "已签收", "order.details.abnormal": "异常", "order.details.shipmentsMethod": "发货方式", "order.details.deliveryPerson": "发货人", "order.details.deliveryTime": "发货时间", "order.details.logisticsCompany": "物流公司", "order.details.trackingNumber": "运单号", "order.details.logisticsStatus": "物流状态", "order.details.packageGoods": "包裹商品", "order.details.logisticsDynamic": "物流动态", "order.details.package": "包裹", "order.evaluate.all": "全部", "order.evaluate.customerReviews": "用户评价", "order.evaluate.defaultReview": "默认评价", "order.evaluate.cancelTop": "取消置顶", "order.evaluate.top": "置顶", "order.evaluate.confirmCancelTop": "是否取消置顶？", "order.evaluate.confirmTop": "是否置顶?", "order.evaluate.reply": "回复", "order.evaluate.reviewContent": "评价内容", "order.evaluate.reviewTime": "评价时间", "order.evaluate.reviewImage": "评价图片", "order.evaluate.productInfo": "商品信息", "order.evaluate.overallReview": "综合评价", "order.evaluate.serviceReview": "服务评价", "order.evaluate.productName": "商品名称", "order.evaluate.enterProductName": "请输入商品名称", "order.evaluate.storeName": "门店名称", "order.evaluate.enterStoreName": "请输入门店名称", "order.evaluate.buyerInfo": "买家信息", "order.evaluate.enterBuyerInfo": "请输入ID/昵称/手机号码", "order.evaluate.topStatus": "置顶状态", "order.evaluate.enterTopStatus": "请选择置顶状态", "order.evaluate.topStatus1": "置顶", "order.evaluate.topStatus2": "未置顶", "order.evaluate.evaluateType": "评价类型", "order.evaluate.enterEvaluateType": "请选择评价类型", "order.evaluate.evaluateType1": "好评", "order.evaluate.evaluateType2": "中评", "order.evaluate.evaluateType3": "差评", "order.evaluate.product": "商品", "order.evaluate.logistics": "物流", "order.evaluate.customerService": "客服", "order.evaluate.displayStatus": "显示状态", "order.evaluate.anonymousUser": "匿名用户", "order.evaluate.enterReplyContent": "请输入回复用户的内容", "order.evaluate.placementSuccess": "置顶成功", "order.evaluate.cancelTopSuccess": "取消置顶", "order.evaluate.cancelTopFailed": "取消置顶失败", "order.evaluate.topFailed": "置顶失败", "order.evaluate.topSuccess": "置顶成功", "order.evaluate.hideSuccess": "隐藏成功", "order.evaluate.cancelHideSuccess": "取消隐藏", "order.evaluate.cancelHideFailed": "取消隐藏失败", "order.evaluate.hideFailed": "隐藏失败", "order.evaluate.hideConfirm": "是否隐藏？", "order.evaluate.deleteConfirm": "删除该评价，是否继续？", "order.evaluate.hideConfirmText": "隐藏后用户无法看到此评价，确认隐藏吗？", "order.evaluate.replySuccess": "回复成功", "order.evaluate.enterReviewContent": "请输入评价内容", "order.evaluate.enterReviewImage": "请上传评价图片", "order.evaluate.enterReviewImageTips": "最多上传5张图片", "order.evaluate.enterReviewImageTips2": "最多上传5张图片", "order.evaluate.replyContent": "评价回复", "order.evaluate.addComment": "新增评论", "order.list.bulkDelivery": "手动批量发货", "order.list.nickname": "买家昵称", "order.list.province": "省份ID", "order.list.city": "城市ID", "order.list.area": "区县ID", "order.list.paymentType": "支付方式", "order.list.shippingType": "配送方式", "order.list.goodsInfoPlaceholder": "商品名称/ID", "order.list.orderNo": "订单号", "order.list.outTradeNo": "交易订单号", "order.list.storeName": "店铺名称", "order.list.orderBulkDelivery": "批量导出", "order.list.exportOrders": "导出订单", "order.list.exportShippingOrders": "导出发货单", "order.list.orderTabAll": "全部", "order.list.orderTabUnpaid": "待付款", "order.list.orderTabUnshipped": "待发货", "order.list.orderTabWaitReceipt": "待收货", "order.list.orderTabReceived": "已收货", "order.list.orderTabCompleted": "已完成", "order.list.orderTabClosed": "交易关闭", "order.list.orderTabCancelled": "已取消", "order.list.goodsInfo": "商品信息", "order.list.orderNumber": "订单编号", "order.list.mainOrderNumber": "主订单号", "order.list.transactionNumber": "交易单号", "order.list.placeOrderTime": "下单时间", "order.list.orderSource": "订单来源", "order.list.exportShippingOrder": "导出发货单", "order.list.all": "全部", "order.list.pendingMerchantProcess": "待商家处理", "order.list.pendingBuyerProcess": "待买家处理", "order.list.pendingMerchantReceive": "待商家收货", "order.list.afterSalesSuccess": "售后成功", "order.list.afterSalesClosed": "售后关闭", "order.list.afterSalesNumber": "售后编号", "order.list.afterSalesTime": "售后时间", "order.list.details": "详情", "order.list.unitPriceQuantity": "单价(元)/数量", "order.list.actualPaymentAmount": "实付金额", "order.list.refundAmount": "申请退款金额", "order.list.orderPaidAmount": "订单实付金额", "order.list.afterSalesTypeRefundAmount": "售后类型/退款金额", "order.list.afterSalesReason": "售后原因", "order.list.productStatus": "商品状态", "order.list.buyerReceiver": "买家/收货人", "order.list.buyer": "买家", "order.list.receiver": "收货人", "order.list.deliveryMethod": "配送方式", "order.list.buyerRemark": "备注", "order.list.orderStatus": "订单状态", "order.list.confirmReceiveGoods": "确定收货吗?", "order.list.confirmReceiveGoodsSuccess": "确认收货成功", "order.list.confirmCompleteOrder": "确定完成订单吗?", "order.list.orderCompleteSuccess": "订单完成成功", "order.list.warning": "警告", "order.list.tip": "提示", "order.list.confirmCancelOrder": "是否取消订单?", "order.list.cancelOrderSuccess": "取消订单成功", "order.list.includeShippingFee": "含运费", "order.list.actuallyPaid": "实付(元)/数量", "order.list.look": "查看", "order.list.alipay": "支付宝", "order.list.wechatPay": "微信", "order.list.balance": "余额", "order.list.selectOrderSrc": "请选择订单来源", "order.list.selectPayMethod": "请选择支付方式", "order.list.buyerInfo": "买家信息", "order.list.enterNickname": "请输入会员昵称", "order.list.selectProvCityDist": "请选择省/市/区", "order.list.selectPaymentMethod": "请选择配送方式", "order.list.sameCityDelivery": "同城配送", "order.list.logisticsDelivery": "物流配送", "order.list.storePickup": "门店自提", "order.list.createTime": "创建时间", "order.list.detailAddr": "详情地址", "order.list.enterDetailAddr": "请输入详情地址", "order.list.selfOperated": "自营", "order.list.noteServNoShip": "注：售后中商品不支持发货", "order.list.selectType": "选择类型", "order.list.shipType": "发货类型", "order.list.courierCompany": "快递公司", "order.list.trackingNumber": "快递单号", "order.list.enterTrackingNumber": "请输入快递单号", "order.list.serviceProvider": "服务商", "order.list.labelTemplate": "面单模版", "order.list.printCtrlNotInst": "打印控件未安装", "order.list.webPrintCLodopNotInst": "Web打印服务CLodop未安装启动，点击这里", "order.list.downloadInst": "下载执行安装", "order.list.alreadyInstalled": "着此前已安装过，", "order.list.reInstIfInstBefore": "可点这里直接再次启动", "order.list.refreshRestartBrowser": "成功后请刷新本页或重启浏览器。", "order.list.privacyShipping": "隐私发货", "order.list.quantity": "数量", "order.list.shipQty": "发货数量", "order.list.afterServiceStatus": "售后状态", "order.list.noLogistics": "无需物流", "order.list.courier": "快递", "order.list.manualEntry": "手动填写", "order.list.eLabel": "电子面单", "order.list.servOne": "服务商一号", "order.list.fillTrackNum": "请填写快递单号", "order.list.selectCourComp": "请选择快递公司", "order.list.selectLogistics": "请选择物流服务", "order.list.orderShip": "订单发货", "order.list.recipient": "收货人", "order.list.enterRecipient": "请输入收货人", "order.list.phoneNumber": "手机号", "order.list.enterPhoneNumber": "请输入手机号", "order.list.deliveryAddress": "收货地址", "order.list.selectProvince": "请选择省份", "order.list.selectCity": "请选择市", "order.list.selectDistrict": "请选择区", "order.list.selectStreet": "请选择街道", "order.list.detailedAddress": "详细地址", "order.list.enterDetailedAddress": "请输入详细地址", "order.list.postalCode": "地址邮编", "order.list.enterPostalCode": "请输入地址邮编", "order.list.postalCodeAdd": "请输入收货地址邮编", "order.list.enterCorrectPhoneNumber": "请输入正确的手机号", "order.list.selectRegion": "请选择地区", "order.list.modifyAddress": "修改地址", "order.list.updateAddressSuccess": "修改地址成功", "order.list.cancelOrder": "取消订单", "order.list.batchExport": "批量导出", "order.list.orderDetail": "订单详情", "order.list.orderRemark": "订单备注", "order.list.deleteOrder": "删除订单", "order.list.printOrder": "小票打印", "order.list.printDelivery": "配货单打印", "order.list.confirmReceipt": "确认收货", "order.list.orderComplete": "订单完成", "order.list.refundImmediately": "立即退款", "order.list.orderInfo": "订单信息", "order.list.afterSale": "售后中", "permissions.common.id": "ID", "permissions.common.department": "部门", "permissions.common.departmentHead": "部门负责人", "permissions.common.remark": "备注", "permissions.common.memberCount": "成员数量", "permissions.common.addDepartment": "添加部门", "permissions.common.editDepartment": "编辑部门", "permissions.common.name": "部门名称", "permissions.common.departmentNameRequired": "请输入不超过15个字的部门名称！", "permissions.common.departmentNameNot50Number": "部门备注不能超过50个字！", "permissions.common.remarkNotNumber": "备注不能为纯数字", "permissions.common.departmentNamePlaceholder": "请输入部门名称", "permissions.common.parentDepartment": "父级部门", "permissions.common.addRole": "添加角色", "permissions.common.editRole": "编辑角色", "permissions.common.roleName": "角色名称", "permissions.common.roleID": "角色ID", "permissions.common.roleRemark": "角色备注", "permissions.common.roleNameRequired": "请输入不超过15个字的部门名称！", "permissions.common.roleNameNot50Number": "部门备注不能超过50个字！", "permissions.common.roleDataPermission": "数据权限", "permissions.common.roleDataPermissionRequired": "请选择数据权限！", "permissions.common.publicLeadPermission": "线索公海池数据权限", "permissions.common.isSetPublicLeadPermission": "是否单独设置", "permissions.common.publicLeadPermissionRequired": "请选择线索公海自定义数据权限！", "permissions.common.rolePermissionRequired": "请至少选择一项！", "permissions.common.rolePermission": "功能权限", "permissions.common.customerPermission": "自定义数据权限", "permissions.common.customerPermissionRequired": "请选择自定义数据权限！", "permissions.common.userName": "员工姓名", "permissions.common.userID": "员工ID", "permissions.common.phoneNumber": "手机号码", "permissions.common.loginAccount": "登录账号", "permissions.common.accountStatus": "账号状态", "permissions.common.userDepartment": "所属部门", "permissions.common.userDepartmentRequired": "请选择所属角色！", "permissions.common.isDepartmentHead": "是否部门负责人", "permissions.common.userRole": "所属角色", "permissions.common.addOrEditUser": "添加/编辑员工", "permissions.common.clueSetting": "线索设置", "permissions.common.assignRoles": "分配角色", "permissions.common.resetPasswords": "重置密码", "permissions.common.registerPhone": "注册手机", "permissions.common.loginPassword": "登录密码", "permissions.common.confirmPassword": "确认密码", "permissions.common.confirmPasswordRequired": "两次密码不一致！", "permissions.common.userRemark": "备注", "permissions.common.registerPhoneRequired": "请输入正确的手机号码！", "permissions.common.registerPhonePlaceholder": "请输入正确的手机号码！", "permissions.common.registerPhoneIsRegistered": "您输入的手机号码已注册！", "permissions.common.userNameRequired": "请输入不超过20位的登录账号！", "permissions.common.employeeNameRequired": "请输入不超过15个字的员工姓名！", "permissions.common.departmentsRequired": "请选择所属部门！", "permissions.common.leadDailyReceiveNum": "每日接收线索容量", "permissions.common.leadPoolLimit": "线索池容量", "permissions.common.deleteDepartmentConfirm": "删除该部门后，部门的数据将流入上级部门，您确定继续删除该部门吗？", "permissions.common.deleteRoleConfirm": "删除角色后，角色信息将无法恢复，您确定要删除该角色吗？", "permissions.common.resetPassword": "重置密码", "permissions.common.resetPasswordConfirm": "重置密码后，当前账号的登录密码将改为初始密码，您确定继续操作吗？", "permissions.common.userList": "员工列表", "permissions.common.roleList": "角色列表", "permissions.common.departmentList": "部门列表", "permissions.common.userNamePlaceholder": "请输入员工姓名", "permissions.common.phoneNumberPlaceholder": "请输入手机号码", "permissions.common.loginAccountPlaceholder": "请输入登录账号", "permissions.common.userRolePlaceholder": "请输入所属角色", "permissions.common.accountStatusPlaceholder": "不限", "permissions.common.accountStatusEnable": "启用", "permissions.common.accountStatusDisable": "禁用", "permissions.common.updateTime": "更新时间", "product.common.addCategory": "新增分类", "product.common.editCategory": "编辑分类", "product.common.deleteCategory": "删除分类", "product.common.categoryName": "分类名称", "product.common.linkProductCount": "关联产品数", "product.common.createOrUpdateTime": "创建/更新时间", "product.common.categoryPlaceholder": "请输入分类名称", "product.common.categoryRequired": "请输入不超过20个字的分类名称！", "product.common.serial": "序号", "product.common.productNumber": "产品编码", "product.common.productName": "产品名称", "product.common.productPicture": "产品图片", "product.common.productCategory": "产品分类", "product.common.productUnit": "单位", "product.common.productPrice": "单价（元）", "product.common.productPriceNoUnit": "单价", "product.common.productDetail": "产品详情", "product.common.unfinishedQuantity": "未完结商机数", "product.common.isSale": "是否上架", "product.common.status": "状态", "product.common.creator": "创建人", "product.common.createTime": "创建时间", "product.common.addProduct": "新增产品", "product.common.editProduct": "编辑产品", "product.common.deleteProduct": "删除", "product.common.productPlaceholder": "请输入产品名称", "product.common.productNameRequired": "请输入不超过30个字的产品名称！", "product.common.productCategoryPlaceholder": "请选择", "product.common.productCategoryRequired": "请选择产品类别", "product.common.productUnitRequired": "请选择产品单位！", "product.common.deleteProductTitle": "删除产品", "profile.common.userID": "账号ID", "profile.common.phoneNumber": "手机号码", "profile.common.loginAccount": "登录账号", "profile.common.accountStatus": "账号状态", "profile.common.userName": "员工姓名", "profile.common.userNameRequired": "请输入不超过15个字的员工姓名！", "profile.common.userDepartment": "所属部门", "profile.common.userRole": "所属角色", "profile.common.remark": "备注", "profile.common.remarkRequired": "员工备注不能超过50个字！", "profile.common.updateTime": "更新时间", "profile.common.editBtn": "修改", "profile.common.resetPswBtn": "重置密码", "profile.common.editPswBtn": "修改密码", "profile.common.editBasicInfo": "基本信息修改", "profile.common.oldPassword": "旧密码", "profile.common.oldPasswordRequired": "旧密码不正确！", "profile.common.newPassword": "登录密码", "profile.common.confirmPassword": "确认密码", "profile.common.passwordRequired": "请输入6-20位的登录密码，由字母、数字组成，区分大小写！", "profile.common.passwordConfirmRequired": "两次密码不一致！", "setting.deal.addAfterSalesSite": "新增售后地址", "setting.deal.afterSalesSiteName": "退货地址名称", "setting.deal.deleteAfterSalesSite": "删除售后地址", "setting.deal.setDefaultAfterSalesSite": "设置默认售后地址", "setting.deal.defaultAfterSalesSite": "默认售后地址", "setting.deal.defaultAfterSalesSiteTip": "设置默认售后地址后，用户在售后申请时，将默认使用该地址", "setting.deal.recipient": "联系人", "setting.deal.enterRecipient": "请输入联系人", "setting.deal.phoneNumber": "退/换货手机号", "setting.deal.enterPhoneNumber": "请输入手机号", "setting.deal.deliveryAddress": "收货地址", "setting.deal.enterDeliveryAddress": "请输入收货地址", "setting.deal.isDefault": "是否默认", "setting.deal.setDefaultAddress": "是否设置为默认地址", "setting.deal.cancelDefaultAddress": "是否取消默认地址", "setting.deal.afterSalesAddress": "退/换货地址", "setting.deal.enterBuyerKeyword": "请输入买家关键字", "setting.deal.addressName": "地址名称", "setting.deal.enterAddressName": "请输入地址名称", "setting.deal.recipientName": "收件人名称", "setting.deal.enterRecipientName": "请输入收件人名称", "setting.deal.recipientPhone": "收件人手机", "setting.deal.enterRecipientPhone": "请输入收件人手机", "setting.deal.enterAfterSalesAddress": "请输入退换货地址", "setting.freightTemplate.pageTitle": "运费模板", "setting.freightTemplate.addButton": "新增模板", "setting.freightTemplate.editButton": "编辑分类", "setting.freightTemplate.deleteButton": "删除分类", "setting.freightTemplate.deleteConfirm": "删除该条数据后不可以恢复，是否继续？", "setting.freightTemplate.logisticsSetting": "物流公司", "setting.freightTemplate.searchKey": "模版搜索", "setting.freightTemplate.namePlaceholder": "请输入模板名称", "setting.freightTemplate.methodType1": "按件数", "setting.freightTemplate.methodType2": "按重量", "setting.freightTemplate.region": "配送区域及运费", "setting.freightTemplate.regionPlaceholder": "请选择配送区域", "setting.freightTemplate.nonRegion": "指定不送达", "setting.freightTemplate.statusEnable": "开启", "setting.freightTemplate.statusDisable": "关闭", "setting.freightTemplate.nonRegionPlaceholder": "请选择指定不送达区域", "setting.freightTemplate.addRegion": "添加配送区域", "setting.freightTemplate.toIsRegion": "可配送区域", "setting.freightTemplate.firstItemCount": "首件数（件）", "setting.freightTemplate.firstItemFee": "运费（元）", "setting.freightTemplate.additionalItemCount": "续件数（件）", "setting.freightTemplate.additionalItemFee": "续费（元）", "setting.freightTemplate.freeThreshold": "满免（元）", "setting.freightTemplate.checkRegion": "选择地区", "setting.freightTemplate.companyType": "快递型号", "setting.freightTemplate.expressageBird": "快递鸟", "setting.freightTemplate.updateInterval": "物流更新", "setting.freightTemplate.updateIntervalPlaceholder": "请输入物流更新", "setting.freightTemplate.updateIntervalHour": "小时", "setting.freightTemplate.updateIntervalTips": "N小时才可更新一次物流，0则表示不做任何限制刷新即更新", "setting.freightTemplate.jdMchCode": "京东商家编码", "setting.freightTemplate.jdMchCodePlaceholder": "请输入京东商家编码", "setting.freightTemplate.requestType": "请求类型", "setting.freightTemplate.requestType1": "免费", "setting.freightTemplate.requestType2": "付费", "setting.freightTemplate.templateSwitch": "模板开关", "setting.freightTemplate.mchId": "商户ID", "setting.freightTemplate.mchIdPlaceholder": "请输入商户ID", "setting.freightTemplate.saveBtn": "保存", "setting.freightTemplate.placePlaceholder": "请输入", "setting.freightTemplate.placeNon": "请填写完整信息", "setting.freightTemplate.editModel": "编辑模板", "setting.freightTemplate.addModel": "新增模板", "setting.freightTemplate.id": "ID", "setting.freightTemplate.name": "模板名称", "setting.freightTemplate.billingMethod": "计费方式", "setting.freightTemplate.isDefault": "是否默认", "setting.freightTemplate.expandCollapseButton": "全部展开/折叠", "setting.mall.pageTitle": "商城设置", "setting.mall.tabType1": "基础设置", "setting.mall.tabType2": "版权设置", "setting.mall.tabType3": "分享设置", "setting.mall.tabType4": "短信设置", "setting.mall.tabType5": "敏感词过滤", "setting.mall.baseName": "商城名称", "setting.mall.placePlaceholderBaseName": "请输入商城名称", "setting.mall.basePhone": "联系电话", "setting.mall.placePlaceholderBasePhone": "请输入联系电话", "setting.mall.baseH5": "H5网站URL", "setting.mall.placeHolderBaseH5": "请输入H5网站URL", "setting.mall.baseLogo": "商城logo", "setting.mall.placeHolderLogo": "请上传商城logo", "setting.mall.baseLogoTips": "建议尺寸：100*100", "setting.mall.copySwitch": "版权开关", "setting.mall.placeCopySwitch": "请选择版权开关", "setting.mall.copyLogo": "logo", "setting.mall.placeHolderCopyLogo": "请上传logo", "setting.mall.copyInfo": "版权信息", "setting.mall.placeHolderCopyInfo": "请输入版权信息", "setting.mall.shareImage": "分享图片", "setting.mall.placeHolderShareImage": "请上传分享图片", "setting.mall.shareTitle": "分享标题", "setting.mall.placeHolderShareTitle": "请输入分享标题", "setting.mall.shareBrief": "分享简介", "setting.mall.placeHolderShareBrief": "请输入分享简介", "setting.mall.smsType": "短信类型", "setting.mall.placeHolderSmsType": "请选择短信类型", "setting.mall.smsType1": "阿里云", "setting.mall.inSmsSet": "国内短信配置", "setting.mall.outSmsSet": "国外短信配置", "setting.mall.outSmsSetTips": "模板内容示例：您的验证码为89757，请勿告知他人", "setting.mall.placeInput": "请输入", "setting.mall.tempName": "模板签名", "setting.mall.placeHolderTempName": "请输入模板签名", "setting.mall.tempId": "模板ID", "setting.mall.placeHolderTempId": "请输入模板ID", "setting.mall.tempSet": "模板变量", "setting.mall.placeHolderTempSet": "请输入模板变量", "setting.mall.sensitiveName": "过滤设置", "setting.mall.sensitiveName1": "百度智能云", "setting.mall.describeTitle": "使用介绍", "setting.mall.describeTips1": "1.请在", "setting.mall.describeTips2": "2.申请文本审核应用", "setting.mall.describeTips3": "3.获取api_key和secret_key,填入下面对应选项中", "setting.mall.isExplainTrue": "收起说明", "setting.mall.isExplainFalse": "展开说明", "setting.mall.statusEnable": "开启", "setting.mall.statusDisable": "关闭", "setting.mall.manualVerificationBtn": "查看示例", "setting.mall.shareTips": "此处添加的为分享网页时展示的图片。", "setting.mall.acceptRe": "申请账号", "setting.mall.placeInfo": "请填写完整信息", "setting.mall.saveError": "操作失败", "setting.mall.saveBtn": "保存", "setting.member.pageTitle": "会员设置", "setting.member.tabType1": "基础配置", "setting.member.tabType2": "奖励配置", "setting.member.defaultLevelName": "默认权重名称设置", "setting.member.placePlaceholder": "请输入", "setting.member.defaultLevelTips": "等用户没有权重的时候默认权重的名称, 未设置时显示为普通用户", "setting.member.memberTitle": "会员登录", "setting.member.multiTerminalLogin": "是否开启单点登录", "setting.member.multiTerminalLoginTips": "开了单点登录后，一个账号在 APP 登录时，之前登录的小程序/公众号/h5会自动退出哦", "setting.member.acceptMobileCode1": "旧号码接收", "setting.member.acceptMobileCode2": "新号码接收", "setting.member.titleSms": "验证码", "setting.member.manualVerificationCode": "是否手动输入获取验证码", "setting.member.manualVerificationTips": "小程序是否手动获取手机号", "setting.member.statusEnable": "开启", "setting.member.statusDisable": "关闭", "setting.member.manualVerificationBtn": "查看示例", "setting.member.acceptMobileCode": "接收更换手机验证码", "setting.member.acceptMobileTips": "前端在会员申请更换手机号码时，系统将根据此处选择的接收验证码手机（新手机号或旧手机号）进行验证码发送。", "setting.member.titleCommon": "通用设置", "setting.member.forceUserProfile": "是否强制填写昵称或头像", "setting.member.forceUserProfileTips": "开启后用户中心页将强制弹出填写昵称或头像", "setting.member.showLogoff": "是否开启注销按钮", "setting.member.showLogoffTips": "开启后将会展示用户的”注销账号“按钮", "setting.member.showReport": "是否开启举报功能", "setting.member.showReportTips": "开启后将会显示用户的”举报投诉“按钮", "setting.member.logOffDel": "账号注销删除用户", "setting.member.logOffDelTips": "开启后账号注销将会删除用户", "setting.member.titleTo": "渠道跳转", "setting.member.scanCodeToApp": "H5扫码跳转至APP", "setting.member.scanCodeToAppTips": "打开以后通过扫描H5的二维码去下载或者是打开APP", "setting.member.titleRecommend": "推荐有礼", "setting.member.recommendRegisterAmount": "赠送积分", "setting.member.recommendRegisterAmountTips": "邀请新用户成功注册后，邀请人将获得相应数额的积分奖励。", "setting.member.recommendRegisterAmountPlace": "请输入赠送积分", "setting.member.register": "积分", "setting.member.titleRegister": "注册有礼", "setting.member.userRegisterAmount": "赠送积分", "setting.member.userRegisterAmountTips": "用户注册后即赠送一定数额的积分。", "setting.member.saveBtn": "保存", "settingSop.common.addLabel": "添加标签", "settingSop.common.addCategory": "添加分类", "settingSop.common.labelName": "标签名称", "settingSop.common.labelColor": "标签底色", "settingSop.common.labelRequired": "请输入不超过10个字的标签名称！", "settingSop.common.labelPlaceholder": "请输入标签名称", "settingSop.common.useCount": "使用次数", "settingSop.common.remark": "备注", "settingSop.common.remarkRequired": "备注不能超过50个字！", "settingSop.common.openStatus": "开启状态", "settingSop.common.categoryName": "分类名称", "settingSop.common.status": "状态", "settingSop.common.applyRange": "适用范围", "settingSop.common.applyRangeRule": "请选择适用范围！", "settingSop.common.categoryPlaceholder": "请输入分类名称", "settingSop.common.categoryRequired": "请输入不超过6个字的分类名称！", "settingSop.common.addOrEditCategory": "添加 / 编辑标签分类", "settingSop.common.addOrEditLabel": "添加 / 编辑标签分类", "settingSop.common.stepTitle": "阶段标题", "settingSop.common.stepTitlePlaceholder": "请输入阶段标题", "settingSop.common.stepKey": "关键阶段", "settingSop.common.taskKey": "关键任务", "settingSop.common.nonTaskKey": "非关键任务", "settingSop.common.stepIsKey": "是否关键阶段", "settingSop.common.addTaskTitle": "添加任务", "settingSop.common.editTaskTitle": "编辑任务", "settingSop.common.taskTitle": "任务标题", "settingSop.common.taskDetails": "任务详情", "settingSop.common.taskIsKey": "是否关键任务", "settingSop.common.taskTitleRequired": "请输入任务标题", "settingSop.common.taskDetailRequired": "请输入200字以内的任务详情", "settingSop.common.taskDetailPlaceholder": "请输入任务详情", "settingSop.common.locationConfiguration": "归属地配置", "settingSop.common.platform": "平台", "settingSop.common.authorizationID": "授权ID", "settingSop.common.accessKeyID": "AccessKey ID", "settingSop.common.accessKeySECRET": "AccessKey SECRET", "settingSop.common.deleteStage": "删除阶段", "settingSop.common.createStage": "创建阶段", "settingSop.common.deleteTask": "删除任务", "settingSop.common.deleteStageConfirm": "删除阶段后，所添加的任务将无法恢复，您确定要删除该阶段吗？", "settingSop.common.deleteTaskConfirm": "删除任务后，数据将无法恢复，您确定要删除该任务吗？", "settingSop.common.unlockNextStage": "需完成阶段内所有关键任务才可以解锁下一个阶段", "settingSop.common.unlockNextStageTip": "若将某阶段设为关键阶段，则该阶段不可跳过，必须完成其中的关键任务，才能解锁下一阶段。", "settingSop.common.messageTemplate": "消息模板列表", "settingSop.common.publicMessageTemplate": "公共消息模版", "settingSop.common.departmentMessageTemplate": "部门负责人消息模版", "settingSop.common.personalMessageTemplate": "个人消息模版", "settingSop.common.groupMessageTemplate": "协作组消息模版", "settingSop.common.updateTime": "更新时间", "settingSop.common.isEnable": "是否启用", "settingSop.common.siteMessage": "站内信通知", "settingSop.common.sopAutoReset": "SOP自动重置", "system.admin.storageSetting": "存储设置", "system.admin.storageLocalTip": "注意：在负载均衡模式下请使用云存储方式。", "system.admin.storageLocalTip2": "本地服务器存储无需额外配置", "system.admin.isSyncMall": "商城存储跟随平台设置", "system.admin.isSyncMallTip": "开启后，商城将采用平台设定的存储方式及配置。", "system.admin.driver": "存储类型", "system.admin.driverLocal": "本地", "system.admin.driverOss": "阿里云", "system.admin.driverCos": "腾讯云", "system.admin.driverQiniu": "七牛云", "system.admin.qiniuImgStyleInterface": "图片样式接口", "system.admin.qiniuDomain": "绑定域名", "system.admin.ossEndpoint": "endpoint或自定义域名", "system.admin.cosArea": "所属地域", "system.admin.cosDomain": "自定义域名", "system.admin.bucket": "存储空间名称（Bucket）", "system.admin.accessPath": "访问路径", "system.admin.accessProtocol": "访问协议", "system.admin.accessKeyId": "Access Key ID", "system.admin.accessKeySecret": "Access Key SECRET", "system.admin.transferLimit": "传输设置", "system.admin.transferLimitTip": "设置上传文件的大小限制", "system.admin.transferLimitTip2": "-1:表示无限制，单位:MB", "system.admin.transferLimitTip3": "上传图片限制", "system.admin.transferLimitTip4": "上传视频限制", "system.admin.transferLimitTip5": "上传音频限制", "system.admin.transferLimitTip6": "上传文件限制", "system.admin.transferLimitTip7": "请输入视频上传限制", "system.admin.transferLimitTip8": "请输入音频上传限制", "system.admin.transferLimitTip9": "请输入文件上传限制", "system.admin.transferLimitTip10": "请输入图片上传限制", "system.admin.smsSetting": "短信设置", "system.admin.smsType": "短信类型", "system.admin.aliyun": "阿里云", "system.admin.qcloud": "腾讯云", "system.admin.submail": "赛邮云", "system.admin.sdkAppId": "SDK App ID", "system.admin.secretId": "SECRET ID", "system.admin.secretKey": "SECRET KEY", "system.admin.appId": "APP ID", "system.admin.appKey": "APP KEY", "system.admin.domestic": "国内短信配置", "system.admin.international": "国际短信配置", "system.admin.templateName": "模板签名", "system.admin.templateId": "模板ID", "system.admin.templateVariable": "模板变量", "system.admin.templateContent": "模板内容示例", "system.admin.templateNameTip": "请输入模板签名", "system.admin.templateIdTip": "请输入模板ID", "system.admin.templateVariableTip": "请输入模板变量", "system.admin.accessKeyIdTip": "请输入access_key_id", "system.admin.accessKeySecretTip": "请输入access_key_secret", "system.admin.appIdTip": "请输入app_id", "system.admin.appKeyTip": "请输入app_key", "system.admin.sdkAppIdTip": "请输入sdk_app_id", "system.admin.secretIdTip": "请输入secret_id", "system.admin.secretKeyTip": "请输入secret_key", "user.common.addBlacklist": "加入黑名单", "user.common.totalSales": "累计消费总额", "user.common.totalOrder": "总订单数", "user.common.recharge": "充值", "user.common.score": "积分", "user.common.userInfo": "用户信息", "user.common.consumptionRecord": "消费记录", "user.common.scoreDetail": "积分明细", "user.common.balanceDetail": "余额明细", "user.common.recommendRecord": "推荐人变更记录", "user.common.friendList": "好友列表", "user.common.basicInfo": "基础信息", "user.common.userIdentity": "用户身份", "user.common.memberOverview": "会员概况", "user.common.memberTag": "会员标签", "user.common.addTag": "添加标签", "user.common.noTag": "暂无标签", "user.common.addMember": "新增会员"}