{"agreement.addAgreement": "Added agreement", "agreement.agreeName": "name", "agreement.agreementContent": "Agreement content", "agreement.agreementEdit": "Editing Agreement", "agreement.agreementName": "Agreement name", "agreement.confirmDeleteData": "It cannot be restored after deleting this piece of data. Will it continue?", "agreement.creationTime": "Creation time", "agreement.enterAgreementContent": "Please enter the protocol content", "agreement.enterAgreementName": "Please enter the protocol name", "agreement.enterAgreementSearch": "Please enter the protocol to search", "agreement.enterNotes": "Please enter a note", "agreement.enterYourContent": "Enter your content!", "agreement.searchAgreement": "Protocol Search", "agreement.warningText": "warn", "application.list.appIdentifier": "Application logo", "application.list.appNameVersion": "Application name/version information", "application.list.applyName": "Application name", "application.list.applyTitle": "Apply title", "application.list.applyTitlePlaceholder": "Please enter the application title", "application.list.author": "author", "application.list.batchInstall": "Bulk installation", "application.list.content": "Application content", "application.list.editApply": "Edit the application", "application.list.editorDescription": "Detailed introduction to the functions and usage methods of the plug-in", "application.list.editorPlaceholder": "Please enter rich text content", "application.list.group": "Application grouping", "application.list.groupPlaceholder": "Please select App Grouping", "application.list.icon": "Application icon", "application.list.install": "Install", "application.list.installConfirm": "Are you sure you want to install the app?", "application.list.introduction": "Application Introduction", "application.list.introductionPlaceholder": "Please enter the application introduction", "application.list.isPublished": "Whether it is on the shelves", "application.list.pleaseSelectApp": "Please select the application first", "application.list.publishConfirm": "After it is launched, the mall users can install this plug-in in the application market. Will they confirm it is launched?", "application.list.synopsis": "Introduction", "application.list.title": "Application List", "application.list.titlePlaceholder": "Please enter a title", "application.list.uninstallConfirm": "Are you sure you want to uninstall this app? \nThis operation is irreversible, please consider it with caution.", "application.list.uninstallSuccess": "Uninstall successfully", "application.list.uninstallText": "uninstall", "application.list.unpublishConfirm": "After removing the application, the mall users cannot install this plug-in in the application market. Are you sure you remove it?", "application.list.updateConfig": "Update configuration", "application.list.updateConfigConfirmContent": "Confirm to update the configuration?", "application.list.updateConfigSuccess": "Updated configuration successfully", "application.list.warning": "warn", "application.paySet.addPrice": "Added price", "application.paySet.addPriceText": "Added price", "application.paySet.alipayPay": "Alipay Payment", "application.paySet.applicationName": "Application name", "application.paySet.applicationSearch": "Application Search", "application.paySet.applicationSearchPlaceholder": "Please enter the application name", "application.paySet.delete": "delete", "application.paySet.inputPrice": "Enter the price", "application.paySet.maxAddPrice": "Up to 3 pieces can be added", "application.paySet.modifySuccess": "Setting successfully", "application.paySet.month": "moon", "application.paySet.no": "no", "application.paySet.noData": "No data yet", "application.paySet.noPaySetting": "No payment is set", "application.paySet.paymentMethod": "Payment method", "application.paySet.paymentTypeRequired": "Please select the payment method", "application.paySet.permanent": "permanent", "application.paySet.pleaseInputPrice": "Please enter the price", "application.paySet.pleaseInputUnit": "Please enter the unit", "application.paySet.setPaySetting": "set up", "application.paySet.setPermanentPrice": "Set permanent price", "application.paySet.setPrice": "Set the price", "application.paySet.submit": "submit", "application.paySet.wechatPay": "WeChat Payment", "application.paySet.year": "Year", "application.paySet.yes": "yes", "application.paySet.yuan": "Yuan", "apply.actualPayment": "Actual payment", "apply.actualPaymentText": "Real payment", "apply.addBuyText": "Submit an order", "apply.agreement": "Service Agreement", "apply.agreementText": "Please read and agree to the service agreement first", "apply.alreadyText": "Go to purchased apps", "apply.applicationInfo": "Application Information", "apply.applicationSearch": "Application Search", "apply.applicationSearchPlaceholder": "Please enter the application name", "apply.applyDetails": "Application details", "apply.applyName": "Application name", "apply.applyPeriod": "Application cycle", "apply.applyPeriodText": "cycle", "apply.bookShop": "Order the store", "apply.buy": "Buy", "apply.buyList": "Purchase history", "apply.buyShop": "Buy a store", "apply.closeOrder": "Cancellation of order successfully", "apply.closeOrderText": "Cancel the order?", "apply.closeUpText": "Untached", "apply.closed": "Closed", "apply.completed": "Completed", "apply.createTime": "Creation time", "apply.doneOrder": "Payment completed", "apply.download": "Install", "apply.expireTime": "Expiry time", "apply.immediatelyPayment": "Pay now", "apply.installSuccess": "Installation successfully", "apply.leaveFor": "You can go", "apply.lookDetails": "check the details", "apply.lookInfo": "View application information", "apply.negativeText": "Cancel", "apply.noApplicationIntroduction": "No application introduction yet", "apply.obligation": "Payment to be paid", "apply.operatorAccount": "Operation account", "apply.orderBuyText": "Order payment", "apply.orderClosed": "Order closed", "apply.orderComplete": "Order completion", "apply.orderCompletes": "Order completed", "apply.orderMsg": "Order information", "apply.orderNo": "Order number", "apply.orderResult": "Order results", "apply.orderText": "Order", "apply.payMode": "Payment method", "apply.payMoney": "Payment amount", "apply.payTextDesc": "Please pay before {time} to avoid cancellation of order timeout", "apply.periodTime": "Validity period", "apply.perpetual": "Term: Permanently valid", "apply.perpetualText": "Permanently effective", "apply.positiveText": "Sure", "apply.priceName": "price", "apply.readText": "Read and agree", "apply.renew": "Renewal", "apply.serveText": "Service Agreement", "apply.stopText": "Cancel an order", "apply.successTime": "Finished time", "apply.title": "hint", "apply.titleText": "hint", "apply.toBuyText": "You can go to [Purchase Record] to view application information", "apply.toMeApply": "Go to my app", "apply.toPay": "Pay now", "apply.toggleDown": "Is the app untopped?", "apply.toggleUp": "Should the app be topped?", "apply.totalPrice": "Total price", "apply.transactionStatus": "Transaction status", "apply.uninstallFail": "Uninstall failed", "apply.uninstallSuccess": "Uninstall successfully", "apply.uninstallText": "The app is about to be uninstalled, will you continue?", "apply.uninstalled": "Uninstalled", "apply.uninstalledText": "Uninstalled", "apply.uninstalling": "Uninstalling", "apply.uninstallingFail": "Uninstall failed", "apply.uninstallingSuccess": "Uninstall successfully", "apply.uninstallingText": "Uninstalling, please wait", "apply.unitPrice": "unit price", "apply.unload": "uninstall", "apply.upText": "Topped successfully", "apply.useCourse": "Usage tutorial", "apply.warningText": "warn", "apply.wxCodePay": "Please use WeChat to scan the QR code to pay", "auth.role.action": "Action", "auth.role.addRole": "Add Role", "auth.role.assignMenuPermission": "Assign <PERSON>u Permissions for {0}", "auth.role.collapseAll": "Collapse All", "auth.role.confirmDelete": "Are you sure you want to delete this role?", "auth.role.deleteFailed": "Delete Failed", "auth.role.deleteSuccess": "Delete Successful", "auth.role.expandAll": "Expand All", "auth.role.getRolePermissionFailed": "Failed to get role permissions", "auth.role.id": "ID", "auth.role.menuPermission": "Menu Permission", "auth.role.remarkNotNumber": "Remark cannot be pure numbers", "auth.role.roleManagement": "Role Permission Management", "auth.role.roleName": "Role Name", "auth.role.roleNameNotNumber": "Role name cannot be pure numbers", "auth.role.roleNamePlaceholder": "Please enter role name/role ID", "auth.role.roleNameRequired": "Please enter role name", "auth.role.searchPlaceholder": "Please enter menu name to search", "auth.role.selectAll": "Select All", "auth.role.setFailed": "Set Failed", "auth.role.setSuccess": "Set Successfully", "common.actionText": "Action", "common.addSuccess": "Add Success", "common.back": "Back", "common.batchDelete": "<PERSON><PERSON> Delete", "common.batchDeleteTip": "Are you sure you want to delete the selected {0} records?", "common.batchDisable": "<PERSON>ch Disable", "common.batchEnable": "Batch Enable", "common.batchImport": "Batch import", "common.cancelText": "Cancel", "common.checkForm": "Please check whether the form is filled out correctly.", "common.chooseText": "Please choose", "common.clearText": "Clear", "common.closeText": "Close", "common.completeText": "Finish", "common.confirmBack": "return", "common.confirmContent": "Are you sure you want to proceed?", "common.confirmText": "Confirm", "common.copyFailed": "Co<PERSON> failed", "common.copySuccess": "Copy successfully", "common.copyText": "copy", "common.createText": "Create", "common.createTimeText": "Create Time", "common.dark": "Dark Theme", "common.deleteConfirmContent": "Are you sure you want to delete this record?", "common.deleteConfirmTitle": "Delete Confirmation", "common.deleteSuccess": "Delete Success", "common.deleteText": "Delete", "common.detailText": "Details", "common.disableText": "Disable", "common.downloadCenter": "Download Center", "common.editPassword": "Edit Password", "common.editSuccess": "Edit Success", "common.editText": "Edit", "common.emptyText": "No data yet", "common.enableText": "Enable", "common.exportSuccess": "Export is successful, please go to the download center to view it", "common.formValidateFailed": "Form verification failed", "common.fullScreenText": "Full Screen", "common.i18nText": "Multilingual", "common.importSuccess": "Imported successfully", "common.inputText": "Please enter", "common.languageText": "Language", "common.light": "Light Theme", "common.loadingText": "Loading...", "common.lockScreenText": "Lock Screen", "common.logout": "Logout", "common.logoutSuccess": "Logout Success", "common.logoutTip": "Are you sure you want to log out", "common.moreText": "More", "common.okText": "OK", "common.operationFailed": "Operation Failed", "common.operationSuccess": "Operation Success", "common.password": "password", "common.previewText": "Preview", "common.queryText": "Query", "common.redo": "Refresh", "common.remarkText": "Remark", "common.reminderText": "Kind tips", "common.resetPSWSuccess": "Password has been modified, please log in again", "common.resetText": "Reset", "common.restoreText": "Rest<PERSON>", "common.saveSuccess": "Save successfully", "common.saveText": "Save", "common.searchText": "Search", "common.selectAtLeastOneRecord": "Please select at least one record", "common.setFailed": "Setting failed", "common.setSuccess": "Setting successfully", "common.sortText": "Sort", "common.sortTip": "Sort value must be greater than or equal to 0, and the smaller the value, the more forward it will be displayed on the front end", "common.statusText": "Status", "common.submitText": "Submit", "common.switchDarkText": "Switch Dark Theme", "common.switchLightText": "Switch Light Theme", "common.systemConfigText": "System Config", "common.taskSubmitted": "The task has been submitted, please check it in the download center", "common.tipsKnow": "knew", "common.tipsText": "Tips", "common.tipsWarningText": "Kind tips", "common.updateSuccess": "Editing successfully", "common.updateTimeText": "Update Time", "common.userCenter": "Personal Center", "common.userSetting": "User Setting", "common.warningText": "warn", "common.welcomeText": "Hello, welcome to learn more about products", "components.colorPicker.horizontal": "Left and left gradient", "components.colorPicker.vertical": "Up and down gradient", "curd.template.addAppointment": "Add Appointment", "curd.template.addressLabel": "Address", "curd.template.addressPlaceholder": "Please enter address", "curd.template.addressRequired": "Please enter address", "curd.template.appointmentTimeLabel": "Appointment Time", "curd.template.appointmentTimeRequired": "Please select appointment time", "curd.template.avatar": "Avatar", "curd.template.batchDelete": "<PERSON><PERSON> Delete", "curd.template.city": "City", "curd.template.clickedButton": "You clicked the {0} button", "curd.template.confirmDeleteRecords": "Are you sure to delete {0} selected records?", "curd.template.createDateLabel": "Create Time", "curd.template.dialogAdd": "Dialog Add", "curd.template.drawerAdd": "Drawer Add", "curd.template.editAppointment": "Edit Appointment", "curd.template.email": "Email", "curd.template.female": "Female", "curd.template.friendIntroduction": "Friend Introduction", "curd.template.gender": "Gender", "curd.template.id": "ID", "curd.template.keywordLabel": "User Info", "curd.template.keywordPlaceholder": "Please enter nickname/account/ID", "curd.template.lastMonth": "Last Month", "curd.template.lastWeek": "Last Week", "curd.template.male": "Male", "curd.template.mobileLabel": "Mobile", "curd.template.mobilePlaceholder": "Please enter mobile number", "curd.template.mobileRequired": "Please enter mobile number", "curd.template.name": "Name", "curd.template.nameLabel": "Name", "curd.template.namePlaceholder": "Please enter name", "curd.template.nameRequired": "Please enter name", "curd.template.nameTooltip": "This is a tooltip", "curd.template.selectAtLeastOneRecord": "Please select at least one record", "curd.template.sourceLabel": "Source", "curd.template.sourcePlaceholder": "Please enter source", "curd.template.sourceRequired": "Please enter source", "curd.template.status": "Status", "curd.template.statusAll": "All", "curd.template.statusDisabled": "Disabled", "curd.template.statusEnabled": "Enabled", "curd.template.statusLabel": "Status", "curd.template.statusPlaceholder": "Please select status", "curd.template.stayTimeLabel": "Stay Time", "curd.template.stayTimeRequired": "Please select stay time", "curd.template.submitSuccess": "Submit Successfully", "curd.template.today": "Today", "curd.template.typeComfort": "Comfort", "curd.template.typeEconomy": "Economy", "curd.template.typeLabel": "Type", "curd.template.typePlaceholder": "Please select type", "curd.template.typeRequired": "Please select type", "curd.template.unknown": "Unknown", "curd.template.yesterday": "Yesterday", "customer.common.addBusiness": "New business opportunities", "customer.common.addCollaborator": "Add a collaborator", "customer.common.addCustomer": "Added customers", "customer.common.addFollowRecord": "Add follow-up record", "customer.common.addTag": "Add a tag", "customer.common.addTime": "Add time", "customer.common.addUpRecord": "Write and follow up", "customer.common.address": "Detailed address", "customer.common.addressPlaceholder": "The detailed address cannot exceed 50 words!", "customer.common.addressRequired": "Please enter the detailed address!", "customer.common.allCustomer": "all", "customer.common.assignTime": "Allocate time", "customer.common.assigner": "Assignee", "customer.common.backToSea": "Return clues", "customer.common.basicInformation": "Basic information", "customer.common.businessName": "Business Opportunity Name", "customer.common.businessNamePlaceholder": "Please enter the business opportunity name", "customer.common.businessNameRequired": "Please enter a business opportunity name with no more than 30 words!", "customer.common.businessNumber": "Number of business opportunities", "customer.common.businessStage": "Business Opportunity Stage", "customer.common.callStatus": "Call status", "customer.common.clear": "Clear", "customer.common.collaborator": "Collaborators", "customer.common.collaboratorAccount": "account", "customer.common.collaboratorDepartment": "department", "customer.common.collaboratorName": "Name", "customer.common.collaboratorPermission": "Business Opportunity Permissions", "customer.common.collaboratorRole": "Role", "customer.common.companyName": "Company Name", "customer.common.companyNamePlaceholder": "Please enter the company name", "customer.common.companyNameRequired": "Please enter the contact name with no more than 30 words!", "customer.common.createdAt": "Creation time", "customer.common.creator": "Created by", "customer.common.currentPosition": "Current location", "customer.common.customer": "Contact", "customer.common.customerAge": "Contact age", "customer.common.customerAgePlaceholder": "Please enter the age of the contact person", "customer.common.customerAgeRequired": "Age can only be pure numbers within two digits!", "customer.common.customerDetail": "Customer details", "customer.common.customerLabel": "Customer Tags", "customer.common.customerName": "Contact name", "customer.common.customerNamePlaceholder": "Please enter the contact name", "customer.common.customerNameRequired": "Please enter the contact name with no more than 30 words!", "customer.common.customerOverview": "Customer profile", "customer.common.customerPhone": "Contact number", "customer.common.customerPhonePlaceholder": "Please enter the contact number", "customer.common.customerPhoneRequired": "Please enter the correct contact number!", "customer.common.customerReturn": "Return to the customer", "customer.common.customerSex": "Contact Gender", "customer.common.customerTransfer": "Customer Transfer", "customer.common.customerTransferReason": "Please enter the transfer reason", "customer.common.customerWechat": "Contact WeChat", "customer.common.customerWechatPlaceholder": "Please enter a clue to contact WeChat", "customer.common.dataPermission": "Data permissions", "customer.common.department": "Department", "customer.common.detail": "Details", "customer.common.detailCustomerName": "Customer Name", "customer.common.director": "Person in charge", "customer.common.editCustomer": "Edit the client", "customer.common.entryMethods": "Conversion method", "kong.addTitle": "Add", "kong.all": "All", "kong.createDate": "Create Date", "kong.description": "Description", "kong.descriptionLabel": "Description", "kong.descriptionPlaceholder": "Please enter description", "kong.dialogAdd": "Dialog Add", "kong.disable": "Disable", "kong.editTitle": "Edit", "kong.enable": "Enable", "kong.id": "ID", "kong.name": "Name", "kong.nameLabel": "Name", "kong.namePlaceholder": "Please enter name", "kong.nameTooltip": "This is a tooltip", "kong.status": "Status", "kong.statusLabel": "Status", "kong.statusPlaceholder": "Please select status", "kong.type": "Type", "kong.typeFour": "Type Four", "kong.typeOne": "Type One", "kong.typePlaceholder": "Please select type", "kong.typeThree": "Type Three", "kong.typeTwo": "Type Two", "kong.typeUnknown": "Unknown", "link.addTitle": "Add Link", "link.addonsName": "Application Name", "link.addonsNamePlaceholder": "Please enter application name", "link.addonsNameRequired": "Please enter application name", "link.all": "All", "link.allow": "Allow", "link.category": "Link Type", "link.categoryPlaceholder": "Please select link type", "link.categoryRequired": "Please select link type", "link.createTime": "Create Time", "link.disable": "Disable", "link.editTitle": "Edit Link", "link.enable": "Enable", "link.id": "ID", "link.isShare": "Shareable", "link.isShareRequired": "Please select whether shareable", "link.key": "Unique Key", "link.keyPlaceholder": "Please enter unique key", "link.keyRequired": "Please enter unique key", "link.notAllow": "Not Allow", "link.status": "Status", "link.statusRequired": "Please select status", "link.title": "Name", "link.titlePlaceholder": "Please enter name", "link.titleRequired": "Please enter name", "link.type": "Type", "link.typePlaceholder": "Please select type", "link.typePlugin": "Application Plugin", "link.typeRequired": "Please select type", "link.typeSystem": "Mall System", "link.typeUnknown": "Unknown Type", "link.updateTime": "Update Time", "link.url": "Jump Link", "link.urlPlaceholder": "Please enter jump link", "link.urlRequired": "Please enter jump link", "linkCategory.addTitle": "Add Category", "linkCategory.createTime": "Create Time", "linkCategory.editTitle": "Edit Category", "linkCategory.id": "ID", "linkCategory.name": "Unique Key", "linkCategory.namePlaceholder": "Please enter unique key", "linkCategory.nameRequired": "Please enter unique key", "linkCategory.title": "Name", "linkCategory.titlePlaceholder": "Please enter name", "linkCategory.titleRequired": "Please enter name", "linkCategory.updateTime": "Update Time"}