<template>
  <n-drawer ref="drawerFormRef" v-model:show="isDrawer" :width="1050">
    <n-drawer-content :title="isEdit ? '编辑商机' : '新增商机'" v-loading="isLoading">
      <div class="text-[16px] text-title-deep-gray font-bold mb-[16px]">基础信息</div>
      <BasicForm ref="basicFormRef" @register="register">
        <template #customer>
          <div class="flex items-center">
            <div v-if="customerInfo" class="rounded-[4px] bg-tag-gray px-[16px] py-[6px] mr-[12px]">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <div
                    class="w-[40px] h-[40px] bg-avatar-blue text-white border-[2px] flex items-center justify-center rounded-full bg-avatar-blue"
                  >
                    {{ customerInfo?.customer_name[0] }}
                  </div>
                  <div class="ml-2">
                    <div class="text-sm text-title-deep-gray">
                      {{ customerInfo?.customer_name }}
                    </div>
                    <div class="text-[12px] text-text-middle-gray">
                      {{ customerInfo?.phone }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <n-button v-else type="info" @click="handleSwitchCustomer">
              {{ !customerInfo ? '选择客户' : '重新选择' }}
            </n-button>
          </div>
        </template>
      </BasicForm>
      <div class="mt-[28px] mb-[10px] flex justify-between items-center">
        <div class="text-[16px] text-title-deep-gray font-bold mb-[16px]">产品信息</div>
        <n-button type="info" @click="showSwitchProductModal">添加产品</n-button>
      </div>
      <div class="rounded-[4px] p-[16px] bg-table-hover-bg">
        <div class="">
          <div class="flex justify-between items-center text-title-deep-gray mb-[4px]">
            <div class="flex items-center">
              <div>整单折扣</div>

              <n-input-number
                class="w-[110px] px-[12px]"
                :show-button="false"
                :min="0"
                :max="100"
                v-model:value="discountOrder"
              />
              <div>%</div>
            </div>
            <div>
              <span>已选中产品：</span>
              <span class="text-blue-deep font-bold mr-[4px]">
                {{ preSelectedProducts.length }}
              </span>
              <span>种</span>
              <span class="ml-[4px]">总金额</span>
              <span class="text-blue-deep font-bold mx-[8px]">
                {{ totalActualPriceWithDiscount }}
              </span>
              <span class="text-blue-deep">元</span>
            </div>
          </div>
          <div class="flex justify-between items-center text-text-middle-gray">
            <div class="">
              <div>
                总金额计算公式为【（A商品售价x数量x折扣）+（B商品售价x数量x折扣）+···】x整单折扣
              </div>
            </div>
            <div type="info">大写金额：{{ convertToChinese(totalActualPriceWithDiscount) }}</div>
          </div>
        </div>
      </div>
      <!-- 产品信息列表 -->
      <BasicTable
        ref="BusinessRef"
        :columns="productColumns"
        :row-key="(row) => row.id"
        :actionColumn="actionColumn"
        :autoScrollX="true"
        :showTableSetting="false"
        :isShowDivider="false"
        :paginate-single-page="false"
        :pagination="false"
      >
        <template #empty>
          <EmptyBlock icon="msg" emptyText="请添加产品" />
        </template>
      </BasicTable>
      <template #footer>
        <n-button @click="closeDrawer">取消</n-button>
        <n-button class="ml-[12px]" type="primary" @click="formSubmit">提交</n-button>
      </template>
    </n-drawer-content>
    <!-- 选择客户 -->
    <SwitchCustomerModal ref="SwitchCustomerModalRef" @select-customer="handleSelectCustomer" />
    <!-- 选择产品 -->
    <SwitchProductModal
      ref="switchProductModalRef"
      :preSelectedProducts="preSelectedProducts"
      @select-product="handleSelectProduct"
    />
  </n-drawer>
</template>

<script lang="ts" setup>
  import { businessFormSchemas, productColumns } from '../config/schemas'
  import { BasicForm, useForm } from '@/components/Form'
  import { ref, nextTick, reactive, h, computed } from 'vue'
  import { BasicColumn, BasicTable, TableAction } from '@/components/Table'
  import { useI18n } from '@/hooks/web/useI18n'
  import SwitchProductModal from './SwitchProductModal.vue'
  import SwitchCustomerModal from './SwitchCustomerModal.vue'

  import { EmptyBlock } from '@/components/Empty'
  import { convertToChinese } from '@/utils/common'
  import {
    addBusiness,
    updateBusiness,
    getCollaboratorsList,
    getBusinessDetail,
  } from '@/api/admin/customer/myCustomer'
  import { getCollaboratorList } from '@/api/admin/business/myBusiness'
  import { useMessage } from 'naive-ui'
  import { CollaboratorList } from '../config/customerTypes'
  import { type ProductItem } from '@/api/admin/product/product'
  import type { CustomerList } from '@/api/admin/business/type'

  import dayjs from 'dayjs'

  const message = useMessage()
  const emit = defineEmits(['reload'])

  const { t } = useI18n()
  const isDrawer = ref<boolean>(false)
  const drawerFormRef = ref()
  const collaboratorTree = ref<CollaboratorList[]>([])
  const isLoading = ref(false)
  const basicFormRef = ref(null)

  // 客户信息
  let customerInfo = reactive()

  const props = defineProps({
    fromType: {
      type: Number,
      default: 0,
    },
    directorName: String,
  })
  const actionColumn: BasicColumn = reactive({
    width: 80,
    title: '操作',
    key: 'action',
    fixed: 'right',
    render(record) {
      return h(TableAction, {
        style: 'text',
        actions: [
          {
            label: t('common.deleteText'),
            onClick: () => {
              handleDeleteProduct(record)
            },
          },
        ],
      })
    },
  })
  const [register, { submit, setFieldsValue, setProps }] = useForm({
    gridProps: { cols: 2, xGap: 32 },
    layout: 'horizontal',
    showActionButtonGroup: false,
    labelPlacement: 'top',
    requireMarkPlacement: 'left',
  })
  // 是否编辑
  const isEdit = ref(false)
  // 商机 ID
  const opportunityId = ref(0)
  // 编辑表单
  const editFormData = ref()
  // 整单折扣
  const discountOrder = ref(100)
  // 打开抽屉
  async function openDrawer(edit: boolean, oppId?: number) {
    isEdit.value = edit

    opportunityId.value = oppId || 0
    nextTick(() => {
      isDrawer.value = true
    })
    // 打开弹窗后初始化表单
    setTimeout(async () => {
      if (isEdit.value) {
        const res = await getBusinessDetail({
          opportunity_id: opportunityId.value,
          scene: 'customer_detail_edit',
        })
        editFormData.value = res
        // 转换时间戳
        editFormData.value.expected_transaction_at = dayjs(
          new Date(editFormData.value.expected_transaction_at * 1000),
        ).format('YYYY-MM-DD')
        // 回写整单折扣
        discountOrder.value = Number(editFormData.value.whole_order_discount)
        // 回写选中商品
        handleSelectProduct({ products: editFormData.value.opportunity_product })
        // initForm()
      } else {
        setProps({
          schemas: businessFormSchemas(collaboratorTree.value),
        })
        // initForm()
      }
    }, 500)
  }
  function closeDrawer() {
    isDrawer.value = false
  }
  // 初始化表单
  async function initForm() {
    collaboratorTree.value = await getCollaboratorList({
      // scene: isEdit.value ? 'edit' : 'add',
      // customer_id: props.customerInfo.customerId,
      // opportunity_id: isEdit.value ? opportunityId.value : '',
    })
    setProps({
      schemas: businessFormSchemas(collaboratorTree.value),
    })

    // 处理 协作员ids
    const ids = collaboratorTree.value
      .filter((item) => {
        return item.is_selected === 1
      })
      .map((item) => {
        return item.user_id
      })

    // setFieldsValue({
    //   director_name: props.directorName,
    //   customer_id: props.customerInfo.customerId,
    //   ...editFormData.value,
    //   user_ids: ids,
    // })
  }

  /**
   * 产品选择
   */
  const switchProductModalRef = ref()
  const showSwitchProductModal = () => {
    switchProductModalRef.value.showModal()
  }

  /**
   * 选择客户
   */
  const SwitchCustomerModalRef = ref()
  function handleSwitchCustomer() {
    SwitchCustomerModalRef.value.showModal()
  }
  // 已选中的客户
  function handleSelectCustomer({ customers }) {
    customerInfo = customers[0]
  }
  // 使用计算属性计算整单折扣价格
  const totalActualPriceWithDiscount = computed(() => {
    return Number((totalActualPrice.value * (discountOrder.value / 100)).toFixed(2))
  })

  // 总实价
  const totalActualPrice = ref(0)
  // 表格实例
  const BusinessRef = ref()
  // 已选中的商品
  const preSelectedProducts = ref<ProductItem[]>([])
  // 选择产品
  const handleSelectProduct = ({ products }) => {
    preSelectedProducts.value = products.map((item) => {
      return {
        ...item,
        quantity: 1,
        price: Number(item.product_price),
        discount: 100,
        actual_price: Number(item.product_price),
        _calculateTotalPrice: calculateTotalPrice,
      }
    }) as ProductItem[]

    calculateActualPrice()

    BusinessRef.value.setTableData(preSelectedProducts.value)
  }
  // 表格删除已选中的产品
  function handleDeleteProduct(row: any) {
    preSelectedProducts.value = preSelectedProducts.value.filter((item) => item.id !== row.id)
    preSelectedProducts.value.forEach((item) => {
      totalActualPrice.value += item.actual_price || 0
    })

    calculateActualPrice()
    BusinessRef.value.setTableData(preSelectedProducts.value)
  }

  /**
   * 计算实价
   * @param row 行数据
   */

  function calculateTotalPrice(row: any) {
    // 获取数量、售价和折扣
    const quantity = row.quantity || 1
    const price = row.price || 0
    const discount = row.discount || 100
    // 计算实价 = 售价 × 数量 × (折扣 / 100)
    const actualPrice = price * quantity * (discount / 100)
    // 更新行数据
    row.actual_price = Number(actualPrice.toFixed(2))

    // 强制更新表格数据
    if (BusinessRef.value) {
      const currentData = BusinessRef.value.getDataSource()
      const newData = [...currentData]
      BusinessRef.value.setTableData(newData)

      // 计算总实价
      calculateActualPrice()
    }
  }

  function calculateActualPrice() {
    totalActualPrice.value = 0
    preSelectedProducts.value.forEach((item) => {
      totalActualPrice.value += item.actual_price || 0
    })
  }
  // 表单提交
  async function formSubmit() {
    const formData = await submit()
    const businessData = BusinessRef.value.getDataSource()

    const params = {
      scene: 'customer_detail_add',
      whole_order_discount: discountOrder.value,
      products: businessData.map((item) => {
        return {
          discount: item.discount,
          num: item.quantity,
          price: item.price,
          product_id: item.id,
        }
      }),
      permission: 1,
      ...formData,
    }

    // console.log(params)
    try {
      isLoading.value = true
      if (isEdit.value) {
        await updateBusiness(opportunityId.value || 0, params, props.fromType)
      } else {
        await addBusiness(params, props.fromType)
      }
      message.success(t('common.operationSuccess'))
    } catch (err: any) {
      message.error(err?.message)
    } finally {
      isLoading.value = false
      closeDrawer()
      emit('reload')
    }
  }
  defineExpose({
    openDrawer,
  })
</script>
