import { BasicColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import {
  renderLabelRelationship,
  renderCustomer,
  renderCallStatus,
  renderFollowStatus,
} from '../../config/renderColumns'

import { formatToDateTime } from '@/utils'

const { t } = useI18n()

export const columns: BasicColumn[] = [
  {
    type: 'selection',
  },
  {
    title: t('leads.common.customer'),
    width: 165,
    minWidth: 165,
    key: 'customer_name',
    resizable: true,
    render(row) {
      return renderCustomer(row)
    },
  },
  {
    title: t('leads.common.rgsLocation'),
    key: 'phone_address',
    resizable: true,
    width: 125,
    minWidth: 125,
  },
  {
    title: t('leads.common.companyName'),
    key: 'company_name',
    resizable: true,
    width: 194,
    minWidth: 194,
  },
  {
    title: t('leads.common.industry'),
    key: 'industry_name',
    resizable: true,
    width: 96,
    minWidth: 96,
  },
  {
    title: t('leads.common.sex'),
    key: 'sex_text',
    resizable: true,
    width: 55,
    minWidth: 55,
  },
  {
    title: t('leads.common.age'),
    key: 'age',
    resizable: true,
    width: 55,
    minWidth: 55,
  },
  {
    title: t('leads.common.wechat'),
    key: 'wechat',
    resizable: true,
    width: 100,
    minWidth: 100,
  },
  {
    title: t('leads.common.callStatus'),
    key: 'call_status_text',
    render(row) {
      return renderCallStatus(row)
    },
    resizable: true,
    width: 90,
    minWidth: 90,
  },
  {
    title: t('leads.common.leadLabel'),
    key: 'label_ids',
    resizable: true,
    width: 278,
    minWidth: 278,
    render(row) {
      return renderLabelRelationship(row)
    },
  },
  {
    title: t('leads.common.entryMethod'),
    key: 'entry_methods',
    resizable: true,
    width: 90,
    minWidth: 90,
  },
  {
    title: t('leads.common.source'),
    key: 'source',
    resizable: true,
    width: 56,
    minWidth: 56,
  },
  {
    title: t('leads.common.followStatus'),
    key: 'follow_status_text',
    resizable: true,
    width: 88,
    minWidth: 88,
    render(row) {
      return renderFollowStatus(row)
    },
  },
  {
    title: t('common.createTimeText'),
    key: 'created_at',
    render(row) {
      return formatToDateTime(row.created_at)
    },
    resizable: true,
    width: 130,
    minWidth: 130,
  },
  {
    title: t('leads.common.otherInfo'),
    key: 'other_info',
  },
]
