import { FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { type ListAllType } from './leadTypes'

const { t } = useI18n()

export function getSchema(listAll: ListAllType): FormSchema[] {
  return [
    {
      field: 'customer_name',
      label: t('leads.common.customerName'),
      component: 'NInput',
      componentProps: {
        maxlength: 30,
        showCount: true,
        placeholder: t('leads.common.customerNamePlaceholder'),
      },
      rules: [
        { required: true, message: t('leads.common.customerNamePlaceholder') },
        {
          pattern: /^.{1,30}$/,
          message: t('leads.common.customerNameRequired'),
        },
      ],
    },
    {
      field: 'phone',
      label: t('leads.common.customerPhone'),
      component: 'NInput',
      componentProps: {
        placeholder: t('leads.common.customerPhonePlaceholder'),
        maxlength: 11,
        showCount: true,
      },
      rules: [
        { required: true, message: t('leads.common.customerPhonePlaceholder') },
        {
          pattern: /^1[3-9]\d{9}$/,
          message: t('leads.common.customerPhoneRequired'),
        },
      ],
    },
    {
      field: 'wechat',
      label: t('leads.common.customerWechat'),
      component: 'NInput',
      componentProps: {
        placeholder: t('leads.common.customerWechatPlaceholder'),
        maxlength: 30,
        showCount: true,
      },
      rules: [
        { required: false },
        {
          pattern: /^.{1,30}$/,
          message: t('leads.common.customerWechatRequired'),
        },
      ],
    },
    {
      field: 'sex',
      label: t('leads.common.customerSex'),
      component: 'NRadio',
      slot: 'sex',
      componentProps: {
        defaultValue: 1,
        options: [
          { label: t('leads.common.customerSexNone'), value: 1 },
          { label: t('leads.common.customerSexMale'), value: 2 },
          { label: t('leads.common.customerSexFemale'), value: 3 },
        ],
      },
    },
    {
      field: 'age',
      label: t('leads.common.customerAge'),
      component: 'NInput',
      componentProps: {
        placeholder: t('leads.common.customerAgePlaceholder'),
        maxlength: 2,
        showCount: true,
      },
      rules: [
        { required: false },
        {
          pattern: /^[0-9]{1,2}$/,
          message: t('leads.common.customerAgeRequired'),
        },
      ],
    },
    {
      field: 'company_name',
      label: t('leads.common.companyName'),
      component: 'NInput',
      componentProps: {
        placeholder: t('leads.common.customerAgePlaceholder'),
        maxlength: 30,
        showCount: true,
      },
      rules: [
        { required: false },
        {
          pattern: /^.{1,30}$/,
          message: t('leads.common.companyNameRequired'),
        },
      ],
    },
    {
      field: 'industry_id',
      component: 'NSelect',
      label: t('leads.common.industry'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholderForm'),
        options: listAll.industryList,
        'label-field': 'industry_name',
        'value-field': 'id',
      },
    },
    {
      field: 'location_array',
      component: 'NCascader',
      label: t('leads.common.location'),
      // slot: 'locationSlot',
      componentProps: {
        options: listAll.regionList,
        labelField: 'name',
        valueField: 'id',
        childrenField: 'children',
        showPath: true,
        checkStrategy: 'child',
        placeholder: t('leads.common.selectPlaceholderForm'),
      },
    },
    {
      field: 'address',
      label: t('leads.common.locationDetail'),
      component: 'NInput',
      componentProps: {
        placeholder: t('leads.common.addressPlaceholder'),
        maxlength: 100,
        showCount: true,
      },
      rules: [
        { required: false },
        {
          pattern: /^.{1,50}$/,
          message: t('leads.common.addressRequired'),
        },
      ],
    },
    {
      field: 'source_id',
      component: 'NSelect',
      label: t('leads.common.sourceOrin'),
      slot: 'sourceWithAccount',
      rules: [
        {
          required: true,
          message: t('customer.common.sourceRequired'),
        },
      ],
      componentProps: {
        placeholder: t('leads.common.selectPlaceholderForm'),
        options: listAll.sourceList,
        'label-field': 'source_name',
        'value-field': 'id',
      },
    },
    {
      field: 'source_account',
      hidden: true,
    },
    {
      field: 'department_id',
      component: 'NCascader',
      label: t('leads.common.leadDepartment'),
      componentProps: {
        placeholder: t('leads.common.selectPlaceholder'),
        options: listAll.seasPoolList,
        'label-field': 'department_name',
        'value-field': 'id',
      },
    },
    {
      field: 'remark',
      component: 'NInput',
      label: t('leads.common.remark'),
      componentProps: {
        maxlength: 100,
        type: 'textarea',
        placeholder: t('leads.common.remarkPlaceholder'),
        showCount: true,
      },
      giProps: {
        span: 2,
      },
    },
  ]
}
