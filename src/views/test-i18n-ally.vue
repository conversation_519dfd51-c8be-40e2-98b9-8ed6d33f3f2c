<template>
  <div class="test-i18n-ally">
    <h1>{{ t('common.fullScreenText') }}</h1>
    <p>{{ t('common.warningText') }}</p>
    <button>{{ t('common.okText') }}</button>
    
    <!-- 测试一些其他翻译键 -->
    <div>
      <span>{{ t('agreement.agreementName') }}</span>
      <span>{{ t('application.list.applyName') }}</span>
    </div>
    
    <!-- 测试不存在的键（应该会被 i18n Ally 标记为错误） -->
    <div>{{ t('nonexistent.key') }}</div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
</script>

<style scoped>
.test-i18n-ally {
  padding: 20px;
}
</style>
